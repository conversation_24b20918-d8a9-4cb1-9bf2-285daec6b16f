# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: ["decord"]
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
av==12.3.0
    # via qwen-vl-utils
certifi==2022.12.7
    # via requests
charset-normalizer==2.1.1
    # via requests
decord==0.6.0
    # via qwen-vl-utils
filelock==3.13.1
    # via torch
    # via triton
fsspec==2024.2.0
    # via torch
idna==3.4
    # via requests
jinja2==3.1.3
    # via torch
markupsafe==2.1.5
    # via jinja2
mpmath==1.3.0
    # via sympy
networkx==3.1
    # via torch
numpy==1.24.1
    # via decord
    # via torchvision
nvidia-cublas-cu12==12.1.3.1
    # via nvidia-cudnn-cu12
    # via nvidia-cusolver-cu12
    # via torch
nvidia-cuda-cupti-cu12==12.1.105
    # via torch
nvidia-cuda-nvrtc-cu12==12.1.105
    # via torch
nvidia-cuda-runtime-cu12==12.1.105
    # via torch
nvidia-cudnn-cu12==9.1.0.70
    # via torch
nvidia-cufft-cu12==11.0.2.54
    # via torch
nvidia-curand-cu12==10.3.2.106
    # via torch
nvidia-cusolver-cu12==11.4.5.107
    # via torch
nvidia-cusparse-cu12==12.1.0.106
    # via nvidia-cusolver-cu12
    # via torch
nvidia-nccl-cu12==2.20.5
    # via torch
nvidia-nvjitlink-cu12==12.6.68
    # via nvidia-cusolver-cu12
    # via nvidia-cusparse-cu12
nvidia-nvtx-cu12==12.1.105
    # via torch
packaging==24.1
    # via qwen-vl-utils
pillow==10.2.0
    # via qwen-vl-utils
    # via torchvision
requests==2.28.1
    # via qwen-vl-utils
sympy==1.12
    # via torch
torch==2.4.0
    # via torchvision
torchvision==0.19.0
triton==3.0.0
    # via torch
typing-extensions==4.9.0
    # via torch
urllib3==1.26.13
    # via requests
