#!/usr/bin/env python3
"""
Qwen2.5-VL 快速Token计算器
简化版本，用于快速估算token消耗
"""

import sys
import os

# 添加当前目录到路径，以便导入token_calculator
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from token_calculator import calculate_conversation_tokens

def quick_calculate():
    """交互式快速计算"""
    print("=" * 60)
    print("Qwen2.5-VL 快速Token计算器")
    print("=" * 60)
    
    # 获取文本输入
    print("\n1. 请输入对话文本:")
    text = input("> ").strip()
    if not text:
        text = "请分析提供的内容"
    
    # 获取图像输入
    print("\n2. 图像文件路径 (多个用空格分隔，回车跳过):")
    image_input = input("> ").strip()
    images = image_input.split() if image_input else []
    
    # 获取视频输入
    print("\n3. 视频文件路径 (多个用空格分隔，回车跳过):")
    video_input = input("> ").strip()
    videos = video_input.split() if video_input else []
    
    # 选择质量配置
    print("\n4. 选择质量配置:")
    print("   1) 经济模式 (低token消耗)")
    print("   2) 标准模式 (默认)")
    print("   3) 高质量模式 (高token消耗)")
    
    quality_choice = input("请选择 (1-3, 默认2): ").strip()
    
    # 配置参数
    image_config = None
    video_config = None
    
    if quality_choice == "1":
        # 经济模式
        image_config = {
            'min_pixels': 256 * 28 * 28,
            'max_pixels': 512 * 28 * 28
        }
        video_config = {
            'fps': 1.0,
            'total_pixels': 64000 * 28 * 28
        }
        print("已选择: 经济模式")
    elif quality_choice == "3":
        # 高质量模式
        image_config = {
            'min_pixels': 512 * 28 * 28,
            'max_pixels': 2048 * 28 * 28
        }
        video_config = {
            'fps': 4.0,
            'total_pixels': 256000 * 28 * 28
        }
        print("已选择: 高质量模式")
    else:
        # 标准模式（默认）
        print("已选择: 标准模式")
    
    print("\n正在计算...")
    
    try:
        # 计算token
        result = calculate_conversation_tokens(
            text=text,
            images=images if images else None,
            videos=videos if videos else None,
            image_config=image_config,
            video_config=video_config
        )
        
        # 显示结果
        print("\n" + "=" * 60)
        print("计算结果")
        print("=" * 60)
        
        print(f"📝 文本内容: {text[:50]}{'...' if len(text) > 50 else ''}")
        print(f"🖼️  图像数量: {len(images)}")
        print(f"🎥 视频数量: {len(videos)}")
        print()
        
        print(f"🔢 总Token数: {result['total_tokens']:,}")
        print(f"   ├─ 文本Token: {result['breakdown']['text_tokens']:,}")
        print(f"   ├─ 图像Token: {result['breakdown']['image_tokens']:,}")
        print(f"   └─ 视频Token: {result['breakdown']['video_tokens']:,}")
        
        # 详细信息
        if images and result['images']:
            print(f"\n📊 图像详情:")
            for i, img_info in enumerate(result['images']):
                if 'error' not in img_info:
                    print(f"   图像{i+1}: {img_info['tokens']:,} tokens")
                    print(f"     原始尺寸: {img_info['original_size']}")
                    print(f"     调整尺寸: {img_info['resized_size']}")
                else:
                    print(f"   图像{i+1}: 处理失败 - {img_info['error']}")
        
        if videos and result['videos']:
            print(f"\n📊 视频详情:")
            for i, vid_info in enumerate(result['videos']):
                if 'error' not in vid_info:
                    print(f"   视频{i+1}: {vid_info['tokens']:,} tokens")
                    print(f"     时长: {vid_info['duration']:.1f}秒")
                    print(f"     采样帧数: {vid_info['sampled_frames']}")
                    print(f"     帧尺寸: {vid_info['resized_size']}")
                else:
                    print(f"   视频{i+1}: 处理失败 - {vid_info['error']}")
        
        # 成本估算
        cost_per_1k = 0.002  # 假设价格，仅供参考
        estimated_cost = result['total_tokens'] / 1000 * cost_per_1k
        print(f"\n💰 估算成本: ${estimated_cost:.4f} USD (假设 ${cost_per_1k}/1K tokens)")
        
        # 性能建议
        print(f"\n💡 性能建议:")
        if result['total_tokens'] > 100000:
            print("   ⚠️  Token数量较高，建议:")
            print("      - 降低图像分辨率配置")
            print("      - 减少视频采样帧率")
            print("      - 考虑分段处理长视频")
        elif result['total_tokens'] > 50000:
            print("   ℹ️  Token数量中等，可考虑优化配置")
        else:
            print("   ✅ Token数量合理")
        
        if videos and any(v.get('duration', 0) > 300 for v in result['videos'] if 'duration' in v):
            print("   ⚠️  检测到长视频(>5分钟)，建议使用更低的采样率")
        
        # 保存选项
        print(f"\n💾 是否保存结果到文件? (y/N): ", end="")
        save_choice = input().strip().lower()
        if save_choice in ['y', 'yes']:
            filename = f"token_result_{int(time.time())}.json"
            import json
            import time
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"   结果已保存到: {filename}")
        
    except Exception as e:
        print(f"\n❌ 计算失败: {e}")
        print("请检查输入文件是否存在且格式正确")
    
    print(f"\n{'='*60}")

def batch_calculate():
    """批量计算模式"""
    print("=" * 60)
    print("批量Token计算模式")
    print("=" * 60)
    
    print("请输入配置文件路径 (JSON格式):")
    config_file = input("> ").strip()
    
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            configs = json.load(f)
        
        total_tokens = 0
        results = []
        
        print(f"\n开始处理 {len(configs)} 个任务...")
        
        for i, config in enumerate(configs, 1):
            print(f"处理任务 {i}/{len(configs)}...")
            
            result = calculate_conversation_tokens(
                text=config.get('text', ''),
                images=config.get('images'),
                videos=config.get('videos'),
                image_config=config.get('image_config'),
                video_config=config.get('video_config')
            )
            
            results.append({
                'task_id': i,
                'tokens': result['total_tokens'],
                'breakdown': result['breakdown']
            })
            total_tokens += result['total_tokens']
        
        print(f"\n批量处理完成!")
        print(f"总Token消耗: {total_tokens:,}")
        print(f"平均每任务: {total_tokens // len(configs):,} tokens")
        
        # 保存批量结果
        output_file = f"batch_result_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_tasks': len(configs),
                    'total_tokens': total_tokens,
                    'average_tokens': total_tokens // len(configs)
                },
                'results': results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"批量结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == 'batch':
        batch_calculate()
    else:
        quick_calculate()

if __name__ == '__main__':
    main()
