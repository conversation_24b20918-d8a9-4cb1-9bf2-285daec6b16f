# Dockerfile of qwenllm/qwenvl:2.5-cu121

ARG CUDA_VERSION=12.1.0
ARG from=nvidia/cuda:${CUDA_VERSION}-cudnn8-devel-ubuntu22.04

FROM ${from} as base

ARG DEBIAN_FRONTEND=noninteractive
RUN <<EOF
apt update -y && apt upgrade -y && apt install -y --no-install-recommends  \
    git \
    git-lfs \
    python3 \
    python3-pip \
    python3-dev \
    wget \
    vim \
    libsndfile1 \
    ccache \
    software-properties-common \
&& rm -rf /var/lib/apt/lists/*
EOF

RUN wget https://github.com/Kitware/CMake/releases/download/v3.26.1/cmake-3.26.1-Linux-x86_64.sh \
    -q -O /tmp/cmake-install.sh \
    && chmod u+x /tmp/cmake-install.sh \
    && mkdir /opt/cmake-3.26.1 \
    && /tmp/cmake-install.sh --skip-license --prefix=/opt/cmake-3.26.1 \
    && rm /tmp/cmake-install.sh \
    && ln -s /opt/cmake-3.26.1/bin/* /usr/local/bin

RUN ln -s /usr/bin/python3 /usr/bin/python

RUN git lfs install

FROM base as dev

WORKDIR /

RUN mkdir -p /data/shared/Qwen

WORKDIR /data/shared/Qwen/

FROM dev as bundle_req
RUN --mount=type=cache,target=/root/.cache/pip pip3 install networkx==3.1
RUN --mount=type=cache,target=/root/.cache/pip pip3 install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 xformers==0.0.28.post3 --index-url https://download.pytorch.org/whl/cu121
RUN --mount=type=cache,target=/root/.cache/pip pip3 install git+https://github.com/huggingface/transformers@f3f6c86582611976e72be054675e2bf0abb5f775  \
    && pip3 install accelerate

COPY ../qwen-vl-utils ./qwen-vl-utils
RUN cd ./qwen-vl-utils \
    && pip3 install .

FROM bundle_req as bundle_vllm

ARG BUNDLE_FLASH_ATTENTION=true

ENV MAX_JOBS=8
ENV NVCC_THREADS=1
ENV TORCH_CUDA_ARCH_LIST="7.0 7.5 8.0 8.6 8.9 9.0+PTX"
ENV VLLM_FA_CMAKE_GPU_ARCHES="80-real;90-real"
ENV CCACHE_DIR=/root/.cache/ccache

RUN --mount=type=cache,target=/root/.cache/ccache \
    --mount=type=cache,target=/root/.cache/pip \
    if [ "$BUNDLE_FLASH_ATTENTION" = "true" ]; then \
        pip3 install --no-build-isolation flash-attn==2.7.2.post1; \
    fi

ARG BUNDLE_VLLM=true

RUN --mount=type=cache,target=/root/.cache/ccache \
    --mount=type=cache,target=/root/.cache/pip \
    if [ "$BUNDLE_VLLM" = "true" ]; then \
    mkdir -p /data/shared/code \
        && cd /data/shared/code \
        && git clone https://github.com/vllm-project/vllm.git \
        && cd vllm \
        && git checkout bf3b79efb82676219a3275764d8fcf4c70097ce5 \
        && pip3 install -r requirements-cuda.txt \
        && pip3 install setuptools-scm \
        && pip3 install . \
        && cd /data/shared/Qwen \
        && rm -rf /data/shared/code/vllm; \
    fi

RUN --mount=type=cache,target=/root/.cache/pip \
    pip3 install \
    gradio==5.4.0 \
    gradio_client==1.4.2 \
    transformers-stream-generator==0.0.4 \
    av

RUN rm -rvf /root/.cache/pip

COPY ../web_demo_mm.py ./
COPY ../web_demo_streaming ./web_demo_streaming

EXPOSE 80
