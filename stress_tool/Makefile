# Qwen2.5-V<PERSON> Stress Testing Tool Makefile

.PHONY: all build release test clean setup-data run-tests help

# Default target
all: build

# Build in debug mode
build:
	@echo "Building stress testing tool (debug mode)..."
	cargo build

# Build in release mode
release:
	@echo "Building stress testing tool (release mode)..."
	cargo build --release

# Run tests
test:
	@echo "Running unit tests..."
	cargo test

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -rf data/
	rm -f run_tests.sh
	rm -f stress_test_results.*

# Setup test data
setup-data:
	@echo "Setting up test data..."
	./setup_test_data.sh

# Run quick tests (requires setup-data first)
run-tests: release setup-data
	@echo "Running quick stress tests..."
	./run_tests.sh

# Run specific test modes
test-text: release
	@echo "Running text-only stress test..."
	./target/release/qwen-stress --mode text --duration 10 --max-concurrency-power 3

test-image: release setup-data
	@echo "Running image+text stress test..."
	./target/release/qwen-stress --mode image --duration 15 --max-concurrency-power 3

test-video: release setup-data
	@echo "Running video+text stress test..."
	./target/release/qwen-stress --mode video --duration 20 --max-concurrency-power 2

test-mixed: release setup-data
	@echo "Running mixed multimodal stress test..."
	./target/release/qwen-stress --mode mixed --duration 25 --max-concurrency-power 2

# Generate reports in different formats
report-json: release
	@echo "Generating JSON report..."
	./target/release/qwen-stress --mode text --duration 30 --output json --output-file results.json

report-csv: release
	@echo "Generating CSV report..."
	./target/release/qwen-stress --mode text --duration 30 --output csv --output-file results.csv

# Performance benchmarks
benchmark-light: release
	@echo "Running light benchmark (up to 16 concurrent)..."
	./target/release/qwen-stress --mode text --max-concurrency-power 4 --duration 60

benchmark-medium: release
	@echo "Running medium benchmark (up to 64 concurrent)..."
	./target/release/qwen-stress --mode text --max-concurrency-power 6 --duration 90

benchmark-heavy: release setup-data
	@echo "Running heavy benchmark (up to 256 concurrent, mixed mode)..."
	./target/release/qwen-stress --mode mixed --max-concurrency-power 8 --duration 120

# Development helpers
check:
	@echo "Running cargo check..."
	cargo check

fmt:
	@echo "Formatting code..."
	cargo fmt

clippy:
	@echo "Running clippy lints..."
	cargo clippy -- -D warnings

# Install dependencies (for development)
deps:
	@echo "Installing development dependencies..."
	@if command -v brew >/dev/null 2>&1; then \
		echo "Installing with Homebrew..."; \
		brew install imagemagick ffmpeg; \
	elif command -v apt-get >/dev/null 2>&1; then \
		echo "Installing with apt-get..."; \
		sudo apt-get update && sudo apt-get install -y imagemagick ffmpeg; \
	else \
		echo "Please install imagemagick and ffmpeg manually"; \
	fi

# Docker support
docker-build:
	@echo "Building Docker image..."
	docker build -t qwen-vl-stress-tool .

docker-run: docker-build
	@echo "Running in Docker container..."
	docker run --rm -it qwen-vl-stress-tool

# Help target
help:
	@echo "Qwen2.5-VL Stress Testing Tool - Available targets:"
	@echo ""
	@echo "Build targets:"
	@echo "  build         - Build in debug mode"
	@echo "  release       - Build in release mode"
	@echo "  test          - Run unit tests"
	@echo "  clean         - Clean build artifacts"
	@echo ""
	@echo "Setup targets:"
	@echo "  setup-data    - Setup test data (images, videos)"
	@echo "  deps          - Install system dependencies"
	@echo ""
	@echo "Testing targets:"
	@echo "  run-tests     - Run all quick tests"
	@echo "  test-text     - Run text-only test"
	@echo "  test-image    - Run image+text test"
	@echo "  test-video    - Run video+text test"
	@echo "  test-mixed    - Run mixed multimodal test"
	@echo ""
	@echo "Reporting targets:"
	@echo "  report-json   - Generate JSON report"
	@echo "  report-csv    - Generate CSV report"
	@echo ""
	@echo "Benchmark targets:"
	@echo "  benchmark-light  - Light benchmark (up to 16 concurrent)"
	@echo "  benchmark-medium - Medium benchmark (up to 64 concurrent)"
	@echo "  benchmark-heavy  - Heavy benchmark (up to 256 concurrent)"
	@echo ""
	@echo "Development targets:"
	@echo "  check         - Run cargo check"
	@echo "  fmt           - Format code"
	@echo "  clippy        - Run clippy lints"
	@echo ""
	@echo "Docker targets:"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run in Docker container"
	@echo ""
	@echo "Usage examples:"
	@echo "  make setup-data              # Setup test data"
	@echo "  make release                 # Build release version"
	@echo "  make test-text               # Quick text test"
	@echo "  make benchmark-medium        # Medium load test"
	@echo "  make report-json             # Generate JSON report"
