use anyhow::Result;
use clap::Parser;
use log::{info, warn, error};
use std::path::PathBuf;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use serde_json::{json, Value};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use reqwest::Client;
use std::fs;

mod config;
use config::Config;

#[derive(Parser)]
#[command(name = "qwen-stress")]
#[command(about = "A stress testing tool for Qwen2.5-VL multimodal dialogue API")]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    config: PathBuf,

    /// API endpoint URL (overrides config file)
    #[arg(short, long)]
    endpoint: Option<String>,

    /// Model name (overrides config file)
    #[arg(short, long)]
    model: Option<String>,

    /// Test mode: text, image, video, mixed
    #[arg(long, default_value = "text")]
    mode: String,

    /// Maximum concurrent requests (2^n, where n=0-10)
    #[arg(long)]
    max_concurrency_power: Option<u8>,

    /// Duration for each concurrency level in seconds
    #[arg(short, long)]
    duration: Option<u64>,

    /// Request timeout in seconds
    #[arg(short, long)]
    timeout: Option<u64>,

    /// Output format: json, csv, console
    #[arg(short, long)]
    output: Option<String>,

    /// Output file path
    #[arg(long)]
    output_file: Option<PathBuf>,

    /// Verbose logging
    #[arg(short, long)]
    verbose: bool,
}

#[derive(Debug, Clone)]
struct TestMetrics {
    concurrency: usize,
    total_requests: Arc<AtomicU64>,
    successful_requests: Arc<AtomicU64>,
    failed_requests: Arc<AtomicU64>,
    total_response_time: Arc<AtomicU64>,
    total_ttft: Arc<AtomicU64>,  // Time to First Token累计
    min_ttft: Arc<AtomicU64>,    // 最小TTFT
    max_ttft: Arc<AtomicU64>,    // 最大TTFT
    total_tokens: Arc<AtomicU64>, // 总token数
    total_prompt_tokens: Arc<AtomicU64>, // 总prompt token数
    total_completion_tokens: Arc<AtomicU64>, // 总completion token数
    start_time: Instant,
}

impl TestMetrics {
    fn new(concurrency: usize) -> Self {
        Self {
            concurrency,
            total_requests: Arc::new(AtomicU64::new(0)),
            successful_requests: Arc::new(AtomicU64::new(0)),
            failed_requests: Arc::new(AtomicU64::new(0)),
            total_response_time: Arc::new(AtomicU64::new(0)),
            total_ttft: Arc::new(AtomicU64::new(0)),
            min_ttft: Arc::new(AtomicU64::new(u64::MAX)),
            max_ttft: Arc::new(AtomicU64::new(0)),
            total_tokens: Arc::new(AtomicU64::new(0)),
            total_prompt_tokens: Arc::new(AtomicU64::new(0)),
            total_completion_tokens: Arc::new(AtomicU64::new(0)),
            start_time: Instant::now(),
        }
    }

    fn record_request(&self, success: bool, response_time_ms: u64, ttft_ms: u64,
                     prompt_tokens: u64, completion_tokens: u64, total_tokens: u64) {
        self.total_requests.fetch_add(1, Ordering::Relaxed);
        if success {
            self.successful_requests.fetch_add(1, Ordering::Relaxed);

            // 记录TTFT统计
            self.total_ttft.fetch_add(ttft_ms, Ordering::Relaxed);

            // 记录token统计
            self.total_tokens.fetch_add(total_tokens, Ordering::Relaxed);
            self.total_prompt_tokens.fetch_add(prompt_tokens, Ordering::Relaxed);
            self.total_completion_tokens.fetch_add(completion_tokens, Ordering::Relaxed);

            // 更新最小TTFT
            let mut current_min = self.min_ttft.load(Ordering::Relaxed);
            while ttft_ms < current_min {
                match self.min_ttft.compare_exchange_weak(
                    current_min, ttft_ms, Ordering::Relaxed, Ordering::Relaxed
                ) {
                    Ok(_) => break,
                    Err(x) => current_min = x,
                }
            }

            // 更新最大TTFT
            let mut current_max = self.max_ttft.load(Ordering::Relaxed);
            while ttft_ms > current_max {
                match self.max_ttft.compare_exchange_weak(
                    current_max, ttft_ms, Ordering::Relaxed, Ordering::Relaxed
                ) {
                    Ok(_) => break,
                    Err(x) => current_max = x,
                }
            }
        } else {
            self.failed_requests.fetch_add(1, Ordering::Relaxed);
        }
        self.total_response_time.fetch_add(response_time_ms, Ordering::Relaxed);
    }

    fn get_stats(&self) -> (f64, f64, f64, f64, f64, f64, f64, f64, f64, f64, f64) {
        let total = self.total_requests.load(Ordering::Relaxed);
        let successful = self.successful_requests.load(Ordering::Relaxed);
        let total_time = self.total_response_time.load(Ordering::Relaxed);
        let total_ttft = self.total_ttft.load(Ordering::Relaxed);
        let min_ttft = self.min_ttft.load(Ordering::Relaxed);
        let max_ttft = self.max_ttft.load(Ordering::Relaxed);
        let total_tokens = self.total_tokens.load(Ordering::Relaxed);
        let total_prompt_tokens = self.total_prompt_tokens.load(Ordering::Relaxed);
        let total_completion_tokens = self.total_completion_tokens.load(Ordering::Relaxed);
        let elapsed = self.start_time.elapsed().as_secs_f64();

        let success_rate = if total > 0 { successful as f64 / total as f64 } else { 0.0 };
        let qps = if elapsed > 0.0 { total as f64 / elapsed } else { 0.0 };
        let avg_response_time = if total > 0 { total_time as f64 / total as f64 } else { 0.0 };
        let avg_ttft = if successful > 0 { total_ttft as f64 / successful as f64 } else { 0.0 };
        let min_ttft_val = if min_ttft == u64::MAX { 0.0 } else { min_ttft as f64 };
        let max_ttft_val = max_ttft as f64;
        let tokens_per_second = if elapsed > 0.0 { total_tokens as f64 / elapsed } else { 0.0 };
        let avg_tokens_per_request = if successful > 0 { total_tokens as f64 / successful as f64 } else { 0.0 };
        let completion_tokens_per_second = if elapsed > 0.0 { total_completion_tokens as f64 / elapsed } else { 0.0 };

        (success_rate, qps, avg_response_time, elapsed, avg_ttft, min_ttft_val, max_ttft_val,
         tokens_per_second, avg_tokens_per_request, completion_tokens_per_second, total_tokens as f64)
    }
}

async fn send_request(client: &Client, endpoint: &str, model_name: &str, mode: &str)
    -> Result<(bool, u64, u64, u64, u64, u64)> {
    let start = Instant::now();

    let payload = match mode {
        "text" => json!({
            "model": model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请简要解释什么是人工智能？"
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "image" => json!({
            "model": model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请描述这张图片的内容"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "video" => json!({
            "model": model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这个视频的内容"
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        "mixed" => json!({
            "model": model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这些多模态内容并给出总结"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.7
        }),
        _ => return Err(anyhow::anyhow!("Unknown mode: {}", mode)),
    };

    // 发送请求并记录首字节时间
    let ttft_start = Instant::now();
    let response = client
        .post(endpoint)
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await;

    match response {
        Ok(resp) => {
            let status = resp.status();
            let success = status.is_success();

            if success {
                // 记录首字节时间（响应头接收完成时间）
                let ttft_ms = ttft_start.elapsed().as_millis() as u64;

                // 解析响应体获取token信息
                match resp.json::<Value>().await {
                    Ok(json_response) => {
                        let total_time_ms = start.elapsed().as_millis() as u64;

                        // 解析usage信息
                        let usage = json_response.get("usage").unwrap_or(&json!({}));
                        let prompt_tokens = usage.get("prompt_tokens").and_then(|v| v.as_u64()).unwrap_or(0);
                        let completion_tokens = usage.get("completion_tokens").and_then(|v| v.as_u64()).unwrap_or(0);
                        let total_tokens = usage.get("total_tokens").and_then(|v| v.as_u64()).unwrap_or(prompt_tokens + completion_tokens);

                        log::debug!("Request successful: TTFT={}ms, Total={}ms, Tokens={}/{}/{}",
                                  ttft_ms, total_time_ms, prompt_tokens, completion_tokens, total_tokens);

                        Ok((success, total_time_ms, ttft_ms, prompt_tokens, completion_tokens, total_tokens))
                    }
                    Err(e) => {
                        warn!("Failed to parse response JSON: {}", e);
                        let total_time_ms = start.elapsed().as_millis() as u64;
                        Ok((false, total_time_ms, ttft_ms, 0, 0, 0))
                    }
                }
            } else {
                warn!("Request failed with HTTP status: {}", status);
                let total_time_ms = start.elapsed().as_millis() as u64;
                Ok((false, total_time_ms, 0, 0, 0, 0))
            }
        }
        Err(e) => {
            let duration = start.elapsed().as_millis() as u64;
            warn!("Request error: {}", e);
            Ok((false, duration, 0, 0, 0, 0))
        }
    }
}

async fn run_concurrency_level(
    client: &Client,
    endpoint: &str,
    model_name: &str,
    mode: &str,
    concurrency: usize,
    duration_secs: u64,
) -> TestMetrics {
    let metrics = TestMetrics::new(concurrency);
    let semaphore = Arc::new(tokio::sync::Semaphore::new(concurrency));
    let end_time = Instant::now() + Duration::from_secs(duration_secs);

    info!("Starting concurrency level {} for {}s", concurrency, duration_secs);

    let mut tasks = Vec::new();

    while Instant::now() < end_time {
        if tasks.len() < concurrency * 2 {
            let client = client.clone();
            let endpoint = endpoint.to_string();
            let model_name = model_name.to_string();
            let mode = mode.to_string();
            let metrics = metrics.clone();
            let semaphore = semaphore.clone();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                match send_request(&client, &endpoint, &model_name, &mode).await {
                    Ok((success, response_time, ttft, prompt_tokens, completion_tokens, total_tokens)) => {
                        metrics.record_request(success, response_time, ttft, prompt_tokens, completion_tokens, total_tokens);
                    }
                    Err(e) => {
                        error!("Request failed: {}", e);
                        metrics.record_request(false, 0, 0, 0, 0, 0);
                    }
                }
            });

            tasks.push(task);
        }

        // Clean up completed tasks
        tasks.retain(|task| !task.is_finished());

        tokio::time::sleep(Duration::from_millis(10)).await;
    }

    // Wait for remaining tasks with timeout
    let timeout_duration = Duration::from_secs(30);
    let remaining_tasks: Vec<_> = tasks.into_iter().collect();

    match tokio::time::timeout(timeout_duration, async {
        for task in remaining_tasks {
            let _ = task.await;
        }
    }).await {
        Ok(_) => info!("All tasks completed successfully"),
        Err(_) => warn!("Some tasks timed out during cleanup"),
    }

    metrics
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Initialize logging
    if args.verbose {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Debug)
            .init();
    } else {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Info)
            .init();
    }

    info!("Starting Qwen2.5-VL stress testing tool");

    // Load configuration
    let mut config = Config::load(&args.config)?;
    config.validate()?;

    // Override config with command line arguments
    if let Some(endpoint) = args.endpoint {
        config.api.endpoint = endpoint;
    }
    if let Some(model) = args.model {
        config.api.model_name = model;
    }
    if let Some(max_power) = args.max_concurrency_power {
        config.test.max_concurrency_power = max_power;
    }
    if let Some(duration) = args.duration {
        config.test.duration_per_level = duration;
    }
    if let Some(timeout) = args.timeout {
        config.test.request_timeout = timeout;
    }
    if let Some(output) = args.output {
        config.output.format = output;
    }
    if let Some(output_file) = args.output_file {
        config.output.file = Some(output_file);
    }

    info!("Mode: {}", args.mode);
    info!("Max concurrency power: {}", config.test.max_concurrency_power);
    info!("Duration per level: {}s", config.test.duration_per_level);
    info!("Using endpoint: {}", config.api.endpoint);
    info!("Using model: {}", config.api.model_name);

    // Create HTTP client
    let client = Client::builder()
        .timeout(Duration::from_secs(config.test.request_timeout))
        .build()?;

    // Test connectivity
    info!("Testing connectivity to endpoint...");
    match send_request(&client, &config.api.endpoint, &config.api.model_name, "text").await {
        Ok((success, response_time, ttft, prompt_tokens, completion_tokens, total_tokens)) => {
            if success {
                info!("✅ Connectivity test successful ({}ms, TTFT: {}ms, Tokens: {}/{}/{})",
                     response_time, ttft, prompt_tokens, completion_tokens, total_tokens);
            } else {
                warn!("⚠️ Connectivity test failed but endpoint is reachable");
            }
        }
        Err(e) => {
            error!("❌ Connectivity test failed: {}", e);
            return Err(e);
        }
    }

    // Run stress test phases
    let concurrency_levels = config.get_concurrency_levels();

    let mut all_results = Vec::new();
    let test_start_time = std::time::SystemTime::now();

    for (phase, &concurrency) in concurrency_levels.iter().enumerate() {
        info!("Phase {}/{}: Testing with {} concurrent requests",
              phase + 1, concurrency_levels.len(), concurrency);

        let metrics = run_concurrency_level(&client, &config.api.endpoint, &config.api.model_name,
                                          &args.mode, concurrency, config.test.duration_per_level).await;
        let (success_rate, qps, avg_response_time, elapsed, avg_ttft, min_ttft, max_ttft,
             tokens_per_second, avg_tokens_per_request, completion_tokens_per_second, total_tokens) = metrics.get_stats();

        println!("\n{}", "=".repeat(80));
        println!("Concurrency Level: {}", concurrency);
        println!("{}", "=".repeat(80));
        println!("Duration: {:.2}s", elapsed);
        println!("Total Requests: {}", metrics.total_requests.load(Ordering::Relaxed));
        println!("Successful: {}", metrics.successful_requests.load(Ordering::Relaxed));
        println!("Failed: {}", metrics.failed_requests.load(Ordering::Relaxed));
        println!("Success Rate: {:.1}%", success_rate * 100.0);
        println!("QPS (Queries/sec): {:.2}", qps);
        println!("Avg Response Time: {:.2}ms", avg_response_time);
        println!("Avg TTFT (Time to First Token): {:.2}ms", avg_ttft);
        println!("Min TTFT: {:.2}ms", min_ttft);
        println!("Max TTFT: {:.2}ms", max_ttft);
        println!("📊 TOKEN STATISTICS:");
        println!("  Total Tokens Generated: {:.0}", total_tokens);
        println!("  Tokens per Second: {:.2}", tokens_per_second);
        println!("  Completion Tokens per Second: {:.2}", completion_tokens_per_second);
        println!("  Avg Tokens per Request: {:.1}", avg_tokens_per_request);
        println!("{}", "=".repeat(80));

        // Store results for report
        all_results.push(json!({
            "concurrency": concurrency,
            "duration_seconds": elapsed,
            "total_requests": metrics.total_requests.load(Ordering::Relaxed),
            "successful_requests": metrics.successful_requests.load(Ordering::Relaxed),
            "failed_requests": metrics.failed_requests.load(Ordering::Relaxed),
            "success_rate": success_rate,
            "qps": qps,  // QPS (Queries Per Second)
            "requests_per_second": qps,
            "avg_response_time_ms": avg_response_time,
            "avg_ttft_ms": avg_ttft,  // Time to First Token
            "min_ttft_ms": min_ttft,
            "max_ttft_ms": max_ttft,
            "ttft_stats": {
                "average": avg_ttft,
                "minimum": min_ttft,
                "maximum": max_ttft
            },
            "token_stats": {
                "total_tokens": total_tokens,
                "tokens_per_second": tokens_per_second,
                "completion_tokens_per_second": completion_tokens_per_second,
                "avg_tokens_per_request": avg_tokens_per_request,
                "total_prompt_tokens": metrics.total_prompt_tokens.load(Ordering::Relaxed),
                "total_completion_tokens": metrics.total_completion_tokens.load(Ordering::Relaxed)
            }
        }));

        // Short cooldown between levels
        if phase < concurrency_levels.len() - 1 {
            info!("Cooling down for {} seconds...", config.test.cooldown_seconds);
            sleep(Duration::from_secs(config.test.cooldown_seconds)).await;
        }
    }

    // Generate JSON report
    let total_requests: u64 = all_results.iter()
        .map(|r| r["total_requests"].as_u64().unwrap_or(0))
        .sum();
    let total_successful: u64 = all_results.iter()
        .map(|r| r["successful_requests"].as_u64().unwrap_or(0))
        .sum();
    let peak_qps = all_results.iter()
        .map(|r| r["requests_per_second"].as_f64().unwrap_or(0.0))
        .fold(0.0, f64::max);
    let total_tokens: f64 = all_results.iter()
        .map(|r| r["token_stats"]["total_tokens"].as_f64().unwrap_or(0.0))
        .sum();
    let peak_tokens_per_second = all_results.iter()
        .map(|r| r["token_stats"]["tokens_per_second"].as_f64().unwrap_or(0.0))
        .fold(0.0, f64::max);

    let report = json!({
        "test_summary": {
            "timestamp": test_start_time,
            "endpoint": config.api.endpoint,
            "model": config.api.model_name,
            "mode": args.mode,
            "total_duration_seconds": all_results.len() as u64 * config.test.duration_per_level,
            "total_requests": total_requests,
            "total_successful": total_successful,
            "total_failed": total_requests - total_successful,
            "overall_success_rate": if total_requests > 0 { total_successful as f64 / total_requests as f64 } else { 0.0 },
            "peak_qps": peak_qps,
            "total_tokens_generated": total_tokens,
            "peak_tokens_per_second": peak_tokens_per_second,
            "concurrency_levels_tested": concurrency_levels.len()
        },
        "concurrency_levels": all_results,
        "performance_curve": all_results.iter().map(|r| json!({
            "concurrency": r["concurrency"],
            "qps": r["requests_per_second"],
            "tokens_per_second": r["token_stats"]["tokens_per_second"],
            "success_rate": r["success_rate"],
            "avg_response_time_ms": r["avg_response_time_ms"],
            "avg_ttft_ms": r["avg_ttft_ms"]
        })).collect::<Vec<_>>()
    });

    // Output report
    let report_json = serde_json::to_string_pretty(&report)?;

    if let Some(output_file) = &config.output.file {
        // Ensure parent directory exists
        if let Some(parent) = output_file.parent() {
            std::fs::create_dir_all(parent)?;
        }
        std::fs::write(output_file, &report_json)?;
        info!("📊 JSON report saved to: {:?}", output_file);
    } else if config.output.format == "json" {
        // Default to benchmark directory for JSON output
        std::fs::create_dir_all("benchmark")?;
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let default_filename = format!("benchmark/stress_test_{}_{}.json", args.mode, timestamp);
        std::fs::write(&default_filename, &report_json)?;
        info!("📊 JSON report saved to: {}", default_filename);
    } else {
        println!("\n📊 JSON Report:");
        println!("{}", report_json);
    }

    info!("🎉 Stress test completed successfully!");
    println!("\n✨ Test Summary:");
    println!("   Total Requests: {}", total_requests);
    println!("   Success Rate: {:.1}%", (total_successful as f64 / total_requests as f64) * 100.0);
    println!("   Peak QPS: {:.2}", peak_qps);
    println!("   Total Tokens Generated: {:.0}", total_tokens);
    println!("   Peak Tokens per Second: {:.2}", peak_tokens_per_second);

    Ok(())
}
