use anyhow::{anyhow, Result};
use rand::seq::SliceRandom;
use std::path::PathBuf;

use crate::config::{DataConfig, MixedScenario};

#[derive(Debug, Clone)]
pub enum RequestType {
    Text,
    Image,
    Video,
    Mixed,
}

#[derive(Debug, Clone)]
pub struct MultimodalRequest {
    pub request_type: RequestType,
    pub text: String,
    pub images: Vec<PathBuf>,
    pub videos: Vec<PathBuf>,
}

#[derive(Clone)]
pub struct RequestGenerator {
    data_config: DataConfig,
}

impl RequestGenerator {
    pub fn new(data_config: DataConfig) -> Self {
        Self {
            data_config,
        }
    }

    pub fn generate_request(&mut self, request_type: &str) -> Result<MultimodalRequest> {
        match request_type {
            "text" => self.generate_text_request(),
            "image" => self.generate_image_request(),
            "video" => self.generate_video_request(),
            "mixed" => self.generate_mixed_request(),
            _ => Err(anyhow!("Unknown request type: {}", request_type)),
        }
    }

    fn generate_text_request(&mut self) -> Result<MultimodalRequest> {
        let mut rng = rand::thread_rng();
        let text = self
            .data_config
            .text_prompts
            .choose(&mut rng)
            .ok_or_else(|| anyhow!("No text prompts available"))?
            .clone();

        Ok(MultimodalRequest {
            request_type: RequestType::Text,
            text,
            images: vec![],
            videos: vec![],
        })
    }

    fn generate_image_request(&mut self) -> Result<MultimodalRequest> {
        if self.data_config.image_paths.is_empty() {
            return Err(anyhow!("No image paths configured"));
        }

        let mut rng = rand::thread_rng();
        let text = self
            .data_config
            .text_prompts
            .choose(&mut rng)
            .unwrap_or(&"请分析这张图片".to_string())
            .clone();

        // Select 1-3 random images
        let num_images = rand::random::<usize>() % 3 + 1;
        let mut images = vec![];

        for _ in 0..num_images.min(self.data_config.image_paths.len()) {
            if let Some(image_path) = self.data_config.image_paths.choose(&mut rng) {
                if !images.contains(image_path) {
                    images.push(image_path.clone());
                }
            }
        }

        Ok(MultimodalRequest {
            request_type: RequestType::Image,
            text,
            images,
            videos: vec![],
        })
    }

    fn generate_video_request(&mut self) -> Result<MultimodalRequest> {
        if self.data_config.video_paths.is_empty() {
            return Err(anyhow!("No video paths configured"));
        }

        let mut rng = rand::thread_rng();
        let text = self
            .data_config
            .text_prompts
            .choose(&mut rng)
            .unwrap_or(&"请分析这个视频".to_string())
            .clone();

        // Select 1 random video (usually only one video per request)
        let video_path = self
            .data_config
            .video_paths
            .choose(&mut rng)
            .ok_or_else(|| anyhow!("No video paths available"))?
            .clone();

        Ok(MultimodalRequest {
            request_type: RequestType::Video,
            text,
            images: vec![],
            videos: vec![video_path],
        })
    }

    fn generate_mixed_request(&mut self) -> Result<MultimodalRequest> {
        let mut rng = rand::thread_rng();

        // Try to use predefined mixed scenarios first
        if !self.data_config.mixed_scenarios.is_empty() {
            if let Some(scenario) = self.data_config.mixed_scenarios.choose(&mut rng) {
                return Ok(MultimodalRequest {
                    request_type: RequestType::Mixed,
                    text: scenario.text.clone(),
                    images: scenario.images.clone(),
                    videos: scenario.videos.clone(),
                });
            }
        }

        // Generate random mixed request
        let text = self
            .data_config
            .text_prompts
            .choose(&mut rng)
            .unwrap_or(&"请分析这些多媒体内容".to_string())
            .clone();

        let mut images = vec![];
        let mut videos = vec![];

        // Add 1-2 images if available
        if !self.data_config.image_paths.is_empty() {
            let num_images = rand::random::<usize>() % 2 + 1;
            for _ in 0..num_images.min(self.data_config.image_paths.len()) {
                if let Some(image_path) = self.data_config.image_paths.choose(&mut rng) {
                    if !images.contains(image_path) {
                        images.push(image_path.clone());
                    }
                }
            }
        }

        // Add 1 video if available
        if !self.data_config.video_paths.is_empty() {
            if let Some(video_path) = self.data_config.video_paths.choose(&mut rng) {
                videos.push(video_path.clone());
            }
        }

        if images.is_empty() && videos.is_empty() {
            return Err(anyhow!("No images or videos available for mixed request"));
        }

        Ok(MultimodalRequest {
            request_type: RequestType::Mixed,
            text,
            images,
            videos,
        })
    }
}

impl std::fmt::Display for RequestType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RequestType::Text => write!(f, "text"),
            RequestType::Image => write!(f, "image"),
            RequestType::Video => write!(f, "video"),
            RequestType::Mixed => write!(f, "mixed"),
        }
    }
}

impl MultimodalRequest {
    pub fn description(&self) -> String {
        match self.request_type {
            RequestType::Text => format!("Text: {}", self.text.chars().take(50).collect::<String>()),
            RequestType::Image => format!(
                "Image+Text: {} images, text: {}",
                self.images.len(),
                self.text.chars().take(30).collect::<String>()
            ),
            RequestType::Video => format!(
                "Video+Text: {} videos, text: {}",
                self.videos.len(),
                self.text.chars().take(30).collect::<String>()
            ),
            RequestType::Mixed => format!(
                "Mixed: {} images, {} videos, text: {}",
                self.images.len(),
                self.videos.len(),
                self.text.chars().take(20).collect::<String>()
            ),
        }
    }

    pub fn estimated_complexity(&self) -> u32 {
        let mut complexity = 1; // Base complexity for text

        // Add complexity for images (estimated based on typical token usage)
        complexity += self.images.len() as u32 * 500;

        // Add complexity for videos (estimated based on typical token usage)
        complexity += self.videos.len() as u32 * 2000;

        complexity
    }
}
