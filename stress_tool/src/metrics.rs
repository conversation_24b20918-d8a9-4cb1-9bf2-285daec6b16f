use chrono::{DateTime, Utc};
use dashmap::DashMap;
use histogram::Histogram;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

use crate::client::RequestResult;
use crate::multimodal::RequestType;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConcurrencyLevelMetrics {
    pub concurrency: usize,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration: Duration,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub success_rate: f64,
    pub requests_per_second: f64,
    pub avg_response_time: Duration,
    pub min_response_time: Duration,
    pub max_response_time: Duration,
    pub p50_response_time: Duration,
    pub p95_response_time: Duration,
    pub p99_response_time: Duration,
    pub total_bytes_transferred: u64,
    pub total_tokens_used: u64,
    pub error_distribution: std::collections::HashMap<String, u64>,
    pub request_type_distribution: std::collections::HashMap<String, u64>,
}

#[derive(Debug)]
pub struct MetricsCollector {
    current_concurrency: AtomicUsize,
    start_time: RwLock<Option<Instant>>,
    level_start_time: RwLock<Option<DateTime<Utc>>>,
    
    // Counters
    total_requests: AtomicU64,
    successful_requests: AtomicU64,
    failed_requests: AtomicU64,
    total_bytes: AtomicU64,
    total_tokens: AtomicU64,
    
    // Response time histogram
    response_times: RwLock<Histogram>,
    
    // Error tracking
    errors: DashMap<String, AtomicU64>,
    
    // Request type tracking
    request_types: DashMap<String, AtomicU64>,
    
    // Level metrics history
    level_metrics: RwLock<Vec<ConcurrencyLevelMetrics>>,
}

impl MetricsCollector {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            current_concurrency: AtomicUsize::new(0),
            start_time: RwLock::new(None),
            level_start_time: RwLock::new(None),
            total_requests: AtomicU64::new(0),
            successful_requests: AtomicU64::new(0),
            failed_requests: AtomicU64::new(0),
            total_bytes: AtomicU64::new(0),
            total_tokens: AtomicU64::new(0),
            response_times: RwLock::new(Histogram::new()),
            errors: DashMap::new(),
            request_types: DashMap::new(),
            level_metrics: RwLock::new(Vec::new()),
        })
    }

    pub fn start_test(&self) {
        *self.start_time.write() = Some(Instant::now());
    }

    pub fn start_concurrency_level(&self, concurrency: usize) {
        self.current_concurrency.store(concurrency, Ordering::Relaxed);
        *self.level_start_time.write() = Some(Utc::now());
        
        // Reset level-specific metrics
        self.total_requests.store(0, Ordering::Relaxed);
        self.successful_requests.store(0, Ordering::Relaxed);
        self.failed_requests.store(0, Ordering::Relaxed);
        self.total_bytes.store(0, Ordering::Relaxed);
        self.total_tokens.store(0, Ordering::Relaxed);
        *self.response_times.write() = Histogram::new();
        self.errors.clear();
        self.request_types.clear();
    }

    pub fn record_request(&self, result: &RequestResult) {
        self.total_requests.fetch_add(1, Ordering::Relaxed);
        
        if result.success {
            self.successful_requests.fetch_add(1, Ordering::Relaxed);
            self.total_bytes.fetch_add(result.response_size as u64, Ordering::Relaxed);
            
            if let Some(tokens) = result.tokens_used {
                self.total_tokens.fetch_add(tokens as u64, Ordering::Relaxed);
            }
        } else {
            self.failed_requests.fetch_add(1, Ordering::Relaxed);
            
            if let Some(error) = &result.error_message {
                let error_key = self.categorize_error(error);
                self.errors
                    .entry(error_key)
                    .or_insert_with(|| AtomicU64::new(0))
                    .fetch_add(1, Ordering::Relaxed);
            }
        }

        // Record response time
        let response_time_ms = result.duration.as_millis() as u64;
        if let Ok(mut histogram) = self.response_times.try_write() {
            let _ = histogram.increment(response_time_ms);
        }

        // Record request type
        let request_type = result.request_type.to_string();
        self.request_types
            .entry(request_type)
            .or_insert_with(|| AtomicU64::new(0))
            .fetch_add(1, Ordering::Relaxed);
    }

    pub fn finish_concurrency_level(&self) -> ConcurrencyLevelMetrics {
        let end_time = Utc::now();
        let start_time = self.level_start_time.read().unwrap_or(end_time);
        let duration = (end_time - start_time).to_std().unwrap_or(Duration::ZERO);
        
        let total_requests = self.total_requests.load(Ordering::Relaxed);
        let successful_requests = self.successful_requests.load(Ordering::Relaxed);
        let failed_requests = self.failed_requests.load(Ordering::Relaxed);
        
        let success_rate = if total_requests > 0 {
            successful_requests as f64 / total_requests as f64
        } else {
            0.0
        };
        
        let requests_per_second = if duration.as_secs_f64() > 0.0 {
            total_requests as f64 / duration.as_secs_f64()
        } else {
            0.0
        };

        let histogram = self.response_times.read();
        let (avg_response_time, min_response_time, max_response_time, p50, p95, p99) = 
            if histogram.entries() > 0 {
                (
                    Duration::from_millis(histogram.mean().unwrap_or(0.0) as u64),
                    Duration::from_millis(histogram.minimum().unwrap_or(0)),
                    Duration::from_millis(histogram.maximum().unwrap_or(0)),
                    Duration::from_millis(histogram.percentile(50.0).unwrap_or(0.0) as u64),
                    Duration::from_millis(histogram.percentile(95.0).unwrap_or(0.0) as u64),
                    Duration::from_millis(histogram.percentile(99.0).unwrap_or(0.0) as u64),
                )
            } else {
                (Duration::ZERO, Duration::ZERO, Duration::ZERO, Duration::ZERO, Duration::ZERO, Duration::ZERO)
            };

        let error_distribution: std::collections::HashMap<String, u64> = self
            .errors
            .iter()
            .map(|entry| (entry.key().clone(), entry.value().load(Ordering::Relaxed)))
            .collect();

        let request_type_distribution: std::collections::HashMap<String, u64> = self
            .request_types
            .iter()
            .map(|entry| (entry.key().clone(), entry.value().load(Ordering::Relaxed)))
            .collect();

        let metrics = ConcurrencyLevelMetrics {
            concurrency: self.current_concurrency.load(Ordering::Relaxed),
            start_time,
            end_time,
            duration,
            total_requests,
            successful_requests,
            failed_requests,
            success_rate,
            requests_per_second,
            avg_response_time,
            min_response_time,
            max_response_time,
            p50_response_time: p50,
            p95_response_time: p95,
            p99_response_time: p99,
            total_bytes_transferred: self.total_bytes.load(Ordering::Relaxed),
            total_tokens_used: self.total_tokens.load(Ordering::Relaxed),
            error_distribution,
            request_type_distribution,
        };

        self.level_metrics.write().push(metrics.clone());
        metrics
    }

    pub fn get_current_stats(&self) -> CurrentStats {
        let total_requests = self.total_requests.load(Ordering::Relaxed);
        let successful_requests = self.successful_requests.load(Ordering::Relaxed);
        let failed_requests = self.failed_requests.load(Ordering::Relaxed);
        
        let success_rate = if total_requests > 0 {
            successful_requests as f64 / total_requests as f64
        } else {
            0.0
        };

        let elapsed = if let Some(start_time) = *self.level_start_time.read() {
            (Utc::now() - start_time).to_std().unwrap_or(Duration::ZERO)
        } else {
            Duration::ZERO
        };

        let requests_per_second = if elapsed.as_secs_f64() > 0.0 {
            total_requests as f64 / elapsed.as_secs_f64()
        } else {
            0.0
        };

        CurrentStats {
            concurrency: self.current_concurrency.load(Ordering::Relaxed),
            total_requests,
            successful_requests,
            failed_requests,
            success_rate,
            requests_per_second,
            elapsed,
        }
    }

    pub fn get_all_level_metrics(&self) -> Vec<ConcurrencyLevelMetrics> {
        self.level_metrics.read().clone()
    }

    fn categorize_error(&self, error_message: &str) -> String {
        if error_message.contains("timeout") || error_message.contains("Timeout") {
            "Timeout".to_string()
        } else if error_message.contains("Connection") || error_message.contains("connection") {
            "Connection Error".to_string()
        } else if error_message.contains("HTTP error 4") {
            "Client Error (4xx)".to_string()
        } else if error_message.contains("HTTP error 5") {
            "Server Error (5xx)".to_string()
        } else if error_message.contains("DNS") || error_message.contains("dns") {
            "DNS Error".to_string()
        } else {
            "Other Error".to_string()
        }
    }
}

#[derive(Debug, Clone)]
pub struct CurrentStats {
    pub concurrency: usize,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub success_rate: f64,
    pub requests_per_second: f64,
    pub elapsed: Duration,
}
