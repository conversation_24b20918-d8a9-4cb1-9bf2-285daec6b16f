use anyhow::Result;
use chrono::Utc;
use csv::Writer;
use serde_json::json;
use std::fs::File;
use std::io::Write;

use crate::config::OutputConfig;
use crate::metrics::ConcurrencyLevelMetrics;

#[derive(Debug, <PERSON>lone)]
pub struct ReportGenerator {
    config: OutputConfig,
}

#[derive(Debug, <PERSON>lone)]
pub struct TestSummary {
    pub total_duration: std::time::Duration,
    pub total_requests: u64,
    pub total_successful: u64,
    pub total_failed: u64,
    pub overall_success_rate: f64,
    pub peak_qps: f64,
    pub peak_concurrency: usize,
    pub bottleneck_analysis: BottleneckAnalysis,
}

#[derive(Debug, <PERSON>lone)]
pub struct BottleneckAnalysis {
    pub bottleneck_type: String,
    pub bottleneck_concurrency: usize,
    pub recommendations: Vec<String>,
}

impl ReportGenerator {
    pub fn new(config: OutputConfig) -> Self {
        Self { config }
    }

    pub async fn generate_report(&self, results: &[ConcurrencyLevelMetrics]) -> Result<()> {
        if results.is_empty() {
            return Ok(());
        }

        let summary = self.generate_summary(results);

        match self.config.format.as_str() {
            "json" => self.generate_json_report(results, &summary).await?,
            "csv" => self.generate_csv_report(results, &summary).await?,
            "console" => self.generate_console_report(results, &summary),
            _ => self.generate_console_report(results, &summary),
        }

        Ok(())
    }

    fn generate_summary(&self, results: &[ConcurrencyLevelMetrics]) -> TestSummary {
        let total_duration = results
            .iter()
            .map(|r| r.duration)
            .sum::<std::time::Duration>();

        let total_requests: u64 = results.iter().map(|r| r.total_requests).sum();
        let total_successful: u64 = results.iter().map(|r| r.successful_requests).sum();
        let total_failed: u64 = results.iter().map(|r| r.failed_requests).sum();

        let overall_success_rate = if total_requests > 0 {
            total_successful as f64 / total_requests as f64
        } else {
            0.0
        };

        let peak_qps = results
            .iter()
            .map(|r| r.requests_per_second)
            .fold(0.0, f64::max);

        let peak_concurrency = results
            .iter()
            .max_by_key(|r| r.concurrency)
            .map(|r| r.concurrency)
            .unwrap_or(0);

        let bottleneck_analysis = self.analyze_bottlenecks(results);

        TestSummary {
            total_duration,
            total_requests,
            total_successful,
            total_failed,
            overall_success_rate,
            peak_qps,
            peak_concurrency,
            bottleneck_analysis,
        }
    }

    fn analyze_bottlenecks(&self, results: &[ConcurrencyLevelMetrics]) -> BottleneckAnalysis {
        // Find the point where performance starts to degrade
        let mut bottleneck_concurrency = 0;
        let mut bottleneck_type = "None".to_string();
        let mut recommendations = Vec::new();

        // Check for QPS plateau or decline
        let mut peak_qps = 0.0;
        let mut peak_concurrency = 0;
        
        for result in results {
            if result.requests_per_second > peak_qps {
                peak_qps = result.requests_per_second;
                peak_concurrency = result.concurrency;
            }
        }

        // Look for performance degradation patterns
        for (i, result) in results.iter().enumerate() {
            // Check for high error rate
            if result.success_rate < 0.95 && result.concurrency > 1 {
                bottleneck_concurrency = result.concurrency;
                bottleneck_type = "High Error Rate".to_string();
                recommendations.push("Reduce concurrency to improve success rate".to_string());
                recommendations.push("Check server capacity and error logs".to_string());
                break;
            }

            // Check for response time degradation
            if result.p95_response_time.as_millis() > 5000 {
                bottleneck_concurrency = result.concurrency;
                bottleneck_type = "High Response Time".to_string();
                recommendations.push("Response times are too high, consider reducing load".to_string());
                recommendations.push("Optimize server performance or scale horizontally".to_string());
                break;
            }

            // Check for QPS plateau
            if i > 0 {
                let prev_result = &results[i - 1];
                let qps_improvement = (result.requests_per_second - prev_result.requests_per_second) 
                    / prev_result.requests_per_second;
                
                if qps_improvement < 0.1 && result.concurrency >= 8 {
                    bottleneck_concurrency = result.concurrency;
                    bottleneck_type = "QPS Plateau".to_string();
                    recommendations.push("QPS is not improving with higher concurrency".to_string());
                    recommendations.push("Server may be CPU or I/O bound".to_string());
                    break;
                }
            }
        }

        // General recommendations based on peak performance
        if peak_concurrency <= 4 {
            recommendations.push("Low optimal concurrency suggests single-threaded bottleneck".to_string());
        } else if peak_concurrency >= 64 {
            recommendations.push("High optimal concurrency suggests good scalability".to_string());
        }

        if bottleneck_type == "None" {
            bottleneck_type = "No Clear Bottleneck".to_string();
            recommendations.push("Performance scales well within tested range".to_string());
            recommendations.push("Consider testing higher concurrency levels".to_string());
        }

        BottleneckAnalysis {
            bottleneck_type,
            bottleneck_concurrency,
            recommendations,
        }
    }

    async fn generate_json_report(
        &self,
        results: &[ConcurrencyLevelMetrics],
        summary: &TestSummary,
    ) -> Result<()> {
        let report = json!({
            "test_summary": {
                "timestamp": Utc::now(),
                "total_duration_seconds": summary.total_duration.as_secs_f64(),
                "total_requests": summary.total_requests,
                "total_successful": summary.total_successful,
                "total_failed": summary.total_failed,
                "overall_success_rate": summary.overall_success_rate,
                "peak_qps": summary.peak_qps,
                "peak_concurrency": summary.peak_concurrency,
                "bottleneck_analysis": {
                    "type": summary.bottleneck_analysis.bottleneck_type,
                    "concurrency": summary.bottleneck_analysis.bottleneck_concurrency,
                    "recommendations": summary.bottleneck_analysis.recommendations
                }
            },
            "concurrency_levels": results,
            "performance_curve": results.iter().map(|r| json!({
                "concurrency": r.concurrency,
                "qps": r.requests_per_second,
                "success_rate": r.success_rate,
                "avg_response_time_ms": r.avg_response_time.as_millis(),
                "p95_response_time_ms": r.p95_response_time.as_millis()
            })).collect::<Vec<_>>()
        });

        let output = serde_json::to_string_pretty(&report)?;

        if let Some(file_path) = &self.config.file {
            std::fs::write(file_path, output)?;
            println!("JSON report saved to: {:?}", file_path);
        } else {
            println!("{}", output);
        }

        Ok(())
    }

    async fn generate_csv_report(
        &self,
        results: &[ConcurrencyLevelMetrics],
        _summary: &TestSummary,
    ) -> Result<()> {
        let output: Box<dyn Write> = if let Some(file_path) = &self.config.file {
            Box::new(File::create(file_path)?)
        } else {
            Box::new(std::io::stdout())
        };

        let mut writer = Writer::from_writer(output);

        // Write header
        writer.write_record(&[
            "concurrency",
            "duration_seconds",
            "total_requests",
            "successful_requests",
            "failed_requests",
            "success_rate",
            "requests_per_second",
            "avg_response_time_ms",
            "min_response_time_ms",
            "max_response_time_ms",
            "p50_response_time_ms",
            "p95_response_time_ms",
            "p99_response_time_ms",
            "total_bytes_transferred",
            "total_tokens_used",
        ])?;

        // Write data rows
        for result in results {
            writer.write_record(&[
                result.concurrency.to_string(),
                result.duration.as_secs_f64().to_string(),
                result.total_requests.to_string(),
                result.successful_requests.to_string(),
                result.failed_requests.to_string(),
                result.success_rate.to_string(),
                result.requests_per_second.to_string(),
                result.avg_response_time.as_millis().to_string(),
                result.min_response_time.as_millis().to_string(),
                result.max_response_time.as_millis().to_string(),
                result.p50_response_time.as_millis().to_string(),
                result.p95_response_time.as_millis().to_string(),
                result.p99_response_time.as_millis().to_string(),
                result.total_bytes_transferred.to_string(),
                result.total_tokens_used.to_string(),
            ])?;
        }

        writer.flush()?;

        if self.config.file.is_some() {
            println!("CSV report saved to: {:?}", self.config.file);
        }

        Ok(())
    }

    fn generate_console_report(&self, results: &[ConcurrencyLevelMetrics], summary: &TestSummary) {
        println!("\n{}", "=".repeat(100));
        println!("                           STRESS TEST FINAL REPORT");
        println!("{}", "=".repeat(100));

        // Test Summary
        println!("\nTEST SUMMARY:");
        println!("  Total Duration: {:.2}s", summary.total_duration.as_secs_f64());
        println!("  Total Requests: {}", summary.total_requests);
        println!("  Successful: {} ({:.1}%)", summary.total_successful, summary.overall_success_rate * 100.0);
        println!("  Failed: {}", summary.total_failed);
        println!("  Peak QPS: {:.2}", summary.peak_qps);
        println!("  Peak Concurrency: {}", summary.peak_concurrency);

        // Performance Curve
        println!("\nPERFORMANCE CURVE:");
        println!("{:<12} {:<10} {:<12} {:<15} {:<15} {:<15}", 
                 "Concurrency", "QPS", "Success%", "Avg RT (ms)", "P95 RT (ms)", "P99 RT (ms)");
        println!("{}", "-".repeat(90));
        
        for result in results {
            println!("{:<12} {:<10.1} {:<12.1} {:<15} {:<15} {:<15}",
                     result.concurrency,
                     result.requests_per_second,
                     result.success_rate * 100.0,
                     result.avg_response_time.as_millis(),
                     result.p95_response_time.as_millis(),
                     result.p99_response_time.as_millis());
        }

        // Bottleneck Analysis
        println!("\nBOTTLENECK ANALYSIS:");
        println!("  Type: {}", summary.bottleneck_analysis.bottleneck_type);
        if summary.bottleneck_analysis.bottleneck_concurrency > 0 {
            println!("  Bottleneck Concurrency: {}", summary.bottleneck_analysis.bottleneck_concurrency);
        }
        println!("  Recommendations:");
        for recommendation in &summary.bottleneck_analysis.recommendations {
            println!("    • {}", recommendation);
        }

        println!("\n{}", "=".repeat(100));
    }
}
