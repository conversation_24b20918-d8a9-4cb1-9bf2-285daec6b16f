use anyhow::{anyhow, Result};
use reqwest::{Client, Response};
use serde_json::{json, Value};
use std::time::{Duration, Instant};
use uuid::Uuid;

use crate::config::ApiConfig;
use crate::multimodal::{MultimodalRequest, RequestType};

#[derive(Debug, <PERSON>lone)]
pub struct ApiClient {
    client: Client,
    config: ApiConfig,
}

#[derive(Debug, <PERSON>lone)]
pub struct RequestResult {
    pub request_id: String,
    pub request_type: RequestType,
    pub start_time: Instant,
    pub end_time: Instant,
    pub duration: Duration,
    pub success: bool,
    pub status_code: Option<u16>,
    pub error_message: Option<String>,
    pub response_size: usize,
    pub tokens_used: Option<u32>,
}

impl ApiClient {
    pub fn new(config: ApiConfig, timeout: Duration) -> Result<Self> {
        let mut client_builder = Client::builder()
            .timeout(timeout)
            .pool_idle_timeout(Duration::from_secs(30))
            .pool_max_idle_per_host(100);

        // Add default headers
        let mut headers = reqwest::header::HeaderMap::new();
        for (key, value) in &config.headers {
            headers.insert(
                key.parse()?,
                value.parse().map_err(|e| anyhow!("Invalid header value: {}", e))?,
            );
        }

        if let Some(token) = &config.auth_token {
            headers.insert(
                reqwest::header::AUTHORIZATION,
                format!("Bearer {}", token).parse()?,
            );
        }

        client_builder = client_builder.default_headers(headers);

        let client = client_builder.build()?;

        Ok(Self { client, config })
    }

    pub async fn send_request(&self, request: &MultimodalRequest) -> RequestResult {
        let request_id = Uuid::new_v4().to_string();
        let start_time = Instant::now();

        let result = self.send_request_internal(request).await;
        let end_time = Instant::now();
        let duration = end_time - start_time;

        match result {
            Ok((status_code, response_size, tokens_used)) => RequestResult {
                request_id,
                request_type: request.request_type.clone(),
                start_time,
                end_time,
                duration,
                success: true,
                status_code: Some(status_code),
                error_message: None,
                response_size,
                tokens_used,
            },
            Err(error) => RequestResult {
                request_id,
                request_type: request.request_type.clone(),
                start_time,
                end_time,
                duration,
                success: false,
                status_code: None,
                error_message: Some(error.to_string()),
                response_size: 0,
                tokens_used: None,
            },
        }
    }

    async fn send_request_internal(
        &self,
        request: &MultimodalRequest,
    ) -> Result<(u16, usize, Option<u32>)> {
        let payload = self.build_request_payload(request).await?;

        let response = self
            .client
            .post(&self.config.endpoint)
            .json(&payload)
            .send()
            .await?;

        let status_code = response.status().as_u16();
        let response_text = response.text().await?;
        let response_size = response_text.len();

        if status_code >= 400 {
            return Err(anyhow!("HTTP error {}: {}", status_code, response_text));
        }

        // Try to extract token usage from response
        let tokens_used = if let Ok(json_response) = serde_json::from_str::<Value>(&response_text) {
            json_response
                .get("usage")
                .and_then(|usage| usage.get("total_tokens"))
                .and_then(|tokens| tokens.as_u64())
                .map(|tokens| tokens as u32)
        } else {
            None
        };

        Ok((status_code, response_size, tokens_used))
    }

    async fn build_request_payload(&self, request: &MultimodalRequest) -> Result<Value> {
        let mut messages = Vec::new();

        // Build message content based on request type
        let content = match &request.request_type {
            RequestType::Text => {
                vec![json!({
                    "type": "text",
                    "text": request.text
                })]
            }
            RequestType::Image => {
                let mut content = vec![];
                
                // Add images
                for image_path in &request.images {
                    let image_data = self.encode_image(image_path).await?;
                    content.push(json!({
                        "type": "image",
                        "image": image_data
                    }));
                }
                
                // Add text
                content.push(json!({
                    "type": "text",
                    "text": request.text
                }));
                
                content
            }
            RequestType::Video => {
                let mut content = vec![];
                
                // Add videos
                for video_path in &request.videos {
                    content.push(json!({
                        "type": "video",
                        "video": video_path.to_string_lossy()
                    }));
                }
                
                // Add text
                content.push(json!({
                    "type": "text",
                    "text": request.text
                }));
                
                content
            }
            RequestType::Mixed => {
                let mut content = vec![];
                
                // Add images
                for image_path in &request.images {
                    let image_data = self.encode_image(image_path).await?;
                    content.push(json!({
                        "type": "image",
                        "image": image_data
                    }));
                }
                
                // Add videos
                for video_path in &request.videos {
                    content.push(json!({
                        "type": "video",
                        "video": video_path.to_string_lossy()
                    }));
                }
                
                // Add text
                content.push(json!({
                    "type": "text",
                    "text": request.text
                }));
                
                content
            }
        };

        messages.push(json!({
            "role": "user",
            "content": content
        }));

        Ok(json!({
            "model": "Qwen2.5-VL-7B-Instruct",
            "messages": messages,
            "max_tokens": 1000,
            "temperature": 0.7,
            "stream": false
        }))
    }

    async fn encode_image(&self, image_path: &std::path::Path) -> Result<String> {
        let image_data = tokio::fs::read(image_path).await?;
        let mime_type = mime_guess::from_path(image_path)
            .first_or_octet_stream()
            .to_string();
        
        let encoded = base64::encode(&image_data);
        Ok(format!("data:{};base64,{}", mime_type, encoded))
    }
}
