use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub api: ApiConfig,
    pub test: TestConfig,
    pub data: DataConfig,
    pub output: OutputConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiConfig {
    pub endpoint: String,
    pub model_name: String,
    pub headers: std::collections::HashMap<String, String>,
    pub auth_token: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestConfig {
    pub max_concurrency_power: u8,
    pub duration_per_level: u64,
    pub request_timeout: u64,
    pub warmup_requests: u32,
    pub cooldown_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataConfig {
    pub text_prompts: Vec<String>,
    pub image_paths: Vec<PathBuf>,
    pub video_paths: Vec<PathBuf>,
    pub mixed_scenarios: Vec<MixedScenario>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MixedScenario {
    pub text: String,
    pub images: Vec<PathBuf>,
    pub videos: Vec<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutputConfig {
    pub format: String,
    pub file: Option<PathBuf>,
    pub include_raw_data: bool,
    pub real_time_display: bool,
}

impl Default for Config {
    fn default() -> Self {
        let mut headers = std::collections::HashMap::new();
        headers.insert("Content-Type".to_string(), "application/json".to_string());
        headers.insert("User-Agent".to_string(), "qwen-vl-stress-tool/0.1.0".to_string());

        Self {
            api: ApiConfig {
                endpoint: "http://localhost:8000/v1/chat/completions".to_string(),
                model_name: "Qwen/Qwen2.5-VL-32B-Instruct".to_string(),
                headers,
                auth_token: None,
            },
            test: TestConfig {
                max_concurrency_power: 10,
                duration_per_level: 30,
                request_timeout: 30,
                warmup_requests: 10,
                cooldown_seconds: 5,
            },
            data: DataConfig {
                text_prompts: vec![
                    "请解释深度学习的基本概念".to_string(),
                    "什么是人工智能？".to_string(),
                    "请描述机器学习的应用场景".to_string(),
                    "如何优化神经网络的性能？".to_string(),
                    "请介绍自然语言处理的发展历程".to_string(),
                ],
                image_paths: vec![],
                video_paths: vec![],
                mixed_scenarios: vec![],
            },
            output: OutputConfig {
                format: "console".to_string(),
                file: None,
                include_raw_data: false,
                real_time_display: true,
            },
        }
    }
}

impl Config {
    pub fn load<P: AsRef<Path>>(path: P) -> Result<Self> {
        if path.as_ref().exists() {
            let content = std::fs::read_to_string(path)?;
            let config: Config = toml::from_str(&content)
                .map_err(|e| anyhow!("Failed to parse config file: {}", e))?;
            Ok(config)
        } else {
            log::warn!("Config file not found, using default configuration");
            Ok(Self::default())
        }
    }

    pub fn save<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    pub fn validate(&self) -> Result<()> {
        // Validate API endpoint
        if self.api.endpoint.is_empty() {
            return Err(anyhow!("API endpoint cannot be empty"));
        }

        // Validate concurrency power
        if self.test.max_concurrency_power > 10 {
            return Err(anyhow!("Max concurrency power cannot exceed 10 (2^10 = 1024)"));
        }

        // Validate duration
        if self.test.duration_per_level == 0 {
            return Err(anyhow!("Duration per level must be greater than 0"));
        }

        // Validate timeout
        if self.test.request_timeout == 0 {
            return Err(anyhow!("Request timeout must be greater than 0"));
        }

        // Validate output format
        match self.output.format.as_str() {
            "console" | "json" | "csv" => {}
            _ => return Err(anyhow!("Invalid output format: {}", self.output.format)),
        }

        // Validate data paths
        for image_path in &self.data.image_paths {
            if !image_path.exists() {
                log::warn!("Image file not found: {:?}", image_path);
            }
        }

        for video_path in &self.data.video_paths {
            if !video_path.exists() {
                log::warn!("Video file not found: {:?}", video_path);
            }
        }

        Ok(())
    }

    pub fn get_concurrency_levels(&self) -> Vec<usize> {
        (0..=self.test.max_concurrency_power)
            .map(|n| 2_usize.pow(n as u32))
            .collect()
    }
}
