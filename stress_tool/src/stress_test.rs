use anyhow::Result;
use futures::future::join_all;
use indicatif::{ProgressBar, ProgressStyle};
use log::{info, warn, error};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::{interval, sleep};

use crate::client::ApiClient;
use crate::config::Config;
use crate::metrics::{MetricsCollector, ConcurrencyLevelMetrics};
use crate::multimodal::RequestGenerator;
use crate::report::ReportGenerator;

pub struct StressTest {
    config: Config,
    client: ApiClient,
    metrics: Arc<MetricsCollector>,
    request_generator: RequestGenerator,
    report_generator: ReportGenerator,
}

impl StressTest {
    pub async fn new(config: Config) -> Result<Self> {
        let timeout = Duration::from_secs(config.test.request_timeout);
        let client = ApiClient::new(config.api.clone(), timeout)?;
        let metrics = MetricsCollector::new();
        let request_generator = RequestGenerator::new(config.data.clone());
        let report_generator = ReportGenerator::new(config.output.clone());

        Ok(Self {
            config,
            client,
            metrics,
            request_generator,
            report_generator,
        })
    }

    pub async fn run(&mut self, test_mode: &str) -> Result<Vec<ConcurrencyLevelMetrics>> {
        info!("Starting stress test in {} mode", test_mode);
        
        self.metrics.start_test();
        let concurrency_levels = self.config.get_concurrency_levels();
        let mut all_results = Vec::new();

        // Warmup phase
        if self.config.test.warmup_requests > 0 {
            info!("Running warmup with {} requests", self.config.test.warmup_requests);
            self.run_warmup(test_mode).await?;
            
            // Cooldown after warmup
            sleep(Duration::from_secs(self.config.test.cooldown_seconds)).await;
        }

        // Main test phases
        for (phase, &concurrency) in concurrency_levels.iter().enumerate() {
            info!("Phase {}/{}: Testing with {} concurrent requests", 
                  phase + 1, concurrency_levels.len(), concurrency);
            
            let result = self.run_concurrency_level(concurrency, test_mode).await?;
            all_results.push(result.clone());
            
            // Display immediate results
            self.display_level_results(&result);
            
            // Cooldown between levels (except for the last one)
            if phase < concurrency_levels.len() - 1 {
                info!("Cooling down for {} seconds", self.config.test.cooldown_seconds);
                sleep(Duration::from_secs(self.config.test.cooldown_seconds)).await;
            }
        }

        // Generate final report
        self.report_generator.generate_report(&all_results).await?;
        
        info!("Stress test completed successfully");
        Ok(all_results)
    }

    async fn run_warmup(&mut self, test_mode: &str) -> Result<()> {
        let semaphore = Arc::new(Semaphore::new(5)); // Low concurrency for warmup
        let mut tasks = Vec::new();

        for _ in 0..self.config.test.warmup_requests {
            let semaphore = semaphore.clone();
            let client = self.client.clone();
            let mut request_generator = self.request_generator.clone();
            let test_mode = test_mode.to_string();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                let request = request_generator.generate_request(&test_mode)?;
                let _result = client.send_request(&request).await;
                Ok::<(), anyhow::Error>(())
            });

            tasks.push(task);
        }

        let results: Result<Vec<_>, _> = join_all(tasks).await.into_iter().collect();
        results?;
        
        info!("Warmup completed");
        Ok(())
    }

    async fn run_concurrency_level(
        &mut self,
        concurrency: usize,
        test_mode: &str,
    ) -> Result<ConcurrencyLevelMetrics> {
        self.metrics.start_concurrency_level(concurrency);
        
        let semaphore = Arc::new(Semaphore::new(concurrency));
        let duration = Duration::from_secs(self.config.test.duration_per_level);
        let start_time = Instant::now();
        
        // Setup progress bar
        let progress_bar = if self.config.output.real_time_display {
            let pb = ProgressBar::new(duration.as_secs());
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len}s ({msg})")
                    .unwrap()
                    .progress_chars("#>-"),
            );
            Some(pb)
        } else {
            None
        };

        // Start real-time stats display
        let metrics_clone = self.metrics.clone();
        let stats_task = if let Some(pb) = &progress_bar {
            let pb_clone = pb.clone();
            Some(tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(1));
                loop {
                    interval.tick().await;
                    let stats = metrics_clone.get_current_stats();
                    pb_clone.set_message(format!(
                        "QPS: {:.1}, Success: {:.1}%, Concurrency: {}",
                        stats.requests_per_second,
                        stats.success_rate * 100.0,
                        stats.concurrency
                    ));
                    pb_clone.set_position(stats.elapsed.as_secs());
                }
            }))
        } else {
            None
        };

        // Main request loop
        let mut request_tasks = Vec::new();
        
        while start_time.elapsed() < duration {
            // Check if we can spawn more tasks
            if request_tasks.len() < concurrency * 2 {
                let semaphore = semaphore.clone();
                let client = self.client.clone();
                let metrics = self.metrics.clone();
                let mut request_generator = self.request_generator.clone();
                let test_mode = test_mode.to_string();

                let task = tokio::spawn(async move {
                    let _permit = semaphore.acquire().await.unwrap();
                    
                    match request_generator.generate_request(&test_mode) {
                        Ok(request) => {
                            let result = client.send_request(&request).await;
                            metrics.record_request(&result);
                        }
                        Err(e) => {
                            warn!("Failed to generate request: {}", e);
                        }
                    }
                });

                request_tasks.push(task);
            }

            // Clean up completed tasks
            request_tasks.retain(|task| !task.is_finished());
            
            // Small delay to prevent busy waiting
            sleep(Duration::from_millis(10)).await;
        }

        // Wait for remaining tasks to complete (with timeout)
        let remaining_timeout = Duration::from_secs(30);
        let remaining_start = Instant::now();
        
        while !request_tasks.is_empty() && remaining_start.elapsed() < remaining_timeout {
            request_tasks.retain(|task| !task.is_finished());
            sleep(Duration::from_millis(100)).await;
        }

        // Force cleanup of remaining tasks
        for task in request_tasks {
            task.abort();
        }

        // Stop stats display
        if let Some(task) = stats_task {
            task.abort();
        }

        if let Some(pb) = progress_bar {
            pb.finish_with_message("Level completed");
        }

        Ok(self.metrics.finish_concurrency_level())
    }

    fn display_level_results(&self, result: &ConcurrencyLevelMetrics) {
        println!("\n{}", "=".repeat(80));
        println!("Concurrency Level: {}", result.concurrency);
        println!("{}", "=".repeat(80));
        println!("Duration: {:.2}s", result.duration.as_secs_f64());
        println!("Total Requests: {}", result.total_requests);
        println!("Successful: {} ({:.1}%)", result.successful_requests, result.success_rate * 100.0);
        println!("Failed: {}", result.failed_requests);
        println!("Requests/sec: {:.2}", result.requests_per_second);
        println!("Avg Response Time: {:.2}ms", result.avg_response_time.as_millis());
        println!("P50 Response Time: {:.2}ms", result.p50_response_time.as_millis());
        println!("P95 Response Time: {:.2}ms", result.p95_response_time.as_millis());
        println!("P99 Response Time: {:.2}ms", result.p99_response_time.as_millis());
        println!("Total Bytes: {:.2} MB", result.total_bytes_transferred as f64 / 1024.0 / 1024.0);
        
        if result.total_tokens_used > 0 {
            println!("Total Tokens: {}", result.total_tokens_used);
        }

        if !result.error_distribution.is_empty() {
            println!("\nError Distribution:");
            for (error_type, count) in &result.error_distribution {
                println!("  {}: {}", error_type, count);
            }
        }

        if !result.request_type_distribution.is_empty() {
            println!("\nRequest Type Distribution:");
            for (request_type, count) in &result.request_type_distribution {
                println!("  {}: {}", request_type, count);
            }
        }
        
        println!("{}", "=".repeat(80));
    }
}
