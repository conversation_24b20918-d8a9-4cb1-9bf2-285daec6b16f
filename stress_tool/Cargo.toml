[package]
name = "qwen-vl-stress-tool"
version = "0.1.0"
edition = "2021"
authors = ["Qwen2.5-VL Team"]
description = "A stress testing tool for Qwen2.5-VL multimodal dialogue API"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
rand = "0.8"
chrono = { version = "0.4", features = ["serde"] }



[dev-dependencies]
tempfile = "3.0"
mockito = "1.0"

[[bin]]
name = "qwen-stress"
path = "src/main.rs"
