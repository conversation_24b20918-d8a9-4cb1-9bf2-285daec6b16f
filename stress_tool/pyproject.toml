[project]
name = "qwen-vl-stress-analysis"
version = "0.1.0"
description = "Performance analysis tools for Qwen2.5-VL stress testing"
authors = [
    {name = "Qwen2.5-VL Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "pandas>=1.3.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "numpy>=1.21.0",
    "requests>=2.25.0",
    "click>=8.0.0",
    "rich>=10.0.0",
    "tabulate>=0.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[project.scripts]
qwen-analyze = "analysis:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
