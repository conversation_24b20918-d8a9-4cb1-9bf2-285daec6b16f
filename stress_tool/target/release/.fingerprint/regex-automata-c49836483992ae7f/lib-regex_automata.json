{"rustc": 10572296413522253241, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 15630646695703972922, "profile": 14620777910751233144, "path": 8534499934204566552, "deps": [[2964536209444415731, "memchr", false, 3251582971112962447], [7325384046744447800, "aho_corasick", false, 7545059733772585041], [9111760993595911334, "regex_syntax", false, 3253800904837166445]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-automata-c49836483992ae7f/dep-lib-regex_automata"}}], "rustflags": [], "metadata": 8878122455581797878, "config": 2202906307356721367, "compile_kind": 0}