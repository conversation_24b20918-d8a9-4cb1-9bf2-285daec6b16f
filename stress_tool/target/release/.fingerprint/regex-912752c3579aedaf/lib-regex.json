{"rustc": 10572296413522253241, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 14620777910751233144, "path": 6144232820457015832, "deps": [[2964536209444415731, "memchr", false, 3251582971112962447], [6314779025451150414, "regex_automata", false, 125334029553205577], [7325384046744447800, "aho_corasick", false, 7545059733772585041], [9111760993595911334, "regex_syntax", false, 3253800904837166445]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-912752c3579aedaf/dep-lib-regex"}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}