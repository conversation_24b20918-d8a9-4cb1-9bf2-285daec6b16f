{"rustc": 10572296413522253241, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 12724100863246979317, "profile": 3996824499291849675, "path": 388510770192421374, "deps": [[6879013155829402151, "clap_derive", false, 12160704460841756788], [7429307784418916438, "clap_builder", false, 14078714485865540644]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-75329ca6a6355780/dep-lib-clap"}}], "rustflags": [], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}