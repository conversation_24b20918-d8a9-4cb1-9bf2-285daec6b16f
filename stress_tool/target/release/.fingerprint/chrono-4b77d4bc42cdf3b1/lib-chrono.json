{"rustc": 10572296413522253241, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 14620777910751233144, "path": 13994099159759075898, "deps": [[10448766010662481490, "num_traits", false, 6279041740262616409], [10633404241517405153, "serde", false, 17954940996598182242], [17958873330977204455, "iana_time_zone", false, 9653274369703631114]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/chrono-4b77d4bc42cdf3b1/dep-lib-chrono"}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}