# Qwen2.5-VL Stress Test Configuration

[api]
# API endpoint URL
endpoint = "https://d07161945-vllm-openaiv092-463-c4nsjzqg-8000.550c.cloud/v1/chat/completions"

# HTTP headers
[api.headers]
"Content-Type" = "application/json"
"User-Agent" = "qwen-vl-stress-tool/0.1.0"

# Optional authentication token
# auth_token = "your-api-token-here"

[test]
# Maximum concurrency power (2^n, where n=0-10)
# This means testing: 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024 concurrent requests
# 完整测试从2^0=1到2^10=1024的所有并发级别
max_concurrency_power = 10

# Duration for each concurrency level in seconds
duration_per_level = 30

# Request timeout in seconds
request_timeout = 30

# Number of warmup requests before starting the test
warmup_requests = 10

# Cooldown time between concurrency levels in seconds
cooldown_seconds = 5

[data]
# Text prompts for testing
text_prompts = [
    "请解释深度学习的基本概念",
    "什么是人工智能？请详细介绍",
    "请描述机器学习的应用场景",
    "如何优化神经网络的性能？",
    "请介绍自然语言处理的发展历程",
    "什么是计算机视觉？它有哪些应用？",
    "请解释强化学习的基本原理",
    "大语言模型是如何工作的？",
    "请分析人工智能的发展趋势",
    "什么是多模态AI？它的优势是什么？"
]

# Image file paths for image+text testing
image_paths = [
    # "data/images/sample1.jpg",
    # "data/images/sample2.png",
    # "data/images/sample3.jpeg"
]

# Video file paths for video+text testing
video_paths = [
    # "data/videos/sample1.mp4",
    # "data/videos/sample2.avi"
]

# Mixed scenarios combining text, images, and videos
[[data.mixed_scenarios]]
text = "请分析这张图片和视频中的内容，并比较它们的异同点"
images = [
    # "data/mixed/image1.jpg"
]
videos = [
    # "data/mixed/video1.mp4"
]

[[data.mixed_scenarios]]
text = "请根据提供的多媒体内容，生成一份详细的分析报告"
images = [
    # "data/mixed/chart.png",
    # "data/mixed/diagram.jpg"
]
videos = [
    # "data/mixed/presentation.mp4"
]

[output]
# Output format: "console", "json", "csv"
format = "console"

# Output file path (optional, if not specified, output to stdout)
# file = "stress_test_results.json"

# Include raw request/response data in output
include_raw_data = false

# Enable real-time display during testing
real_time_display = true
