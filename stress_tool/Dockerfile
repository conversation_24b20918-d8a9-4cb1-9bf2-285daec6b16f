# Multi-stage build for Qwen2.5-VL Stress Testing Tool

# Build stage
FROM rust:1.75-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy Cargo files
COPY Cargo.toml Cargo.lock ./

# Copy source code
COPY src/ ./src/

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    imagemagick \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false -m -d /app appuser

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/target/release/qwen-stress /usr/local/bin/qwen-stress

# Copy configuration and scripts
COPY config.toml ./
COPY setup_test_data.sh ./
COPY README.md ./

# Make scripts executable
RUN chmod +x setup_test_data.sh

# Change ownership to app user
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Create data directory
RUN mkdir -p data/{images,videos,mixed}

# Setup test data
RUN ./setup_test_data.sh

# Expose any ports if needed (not typically required for stress testing)
# EXPOSE 8080

# Set default command
CMD ["qwen-stress", "--help"]

# Health check (optional)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD qwen-stress --help > /dev/null || exit 1

# Labels for metadata
LABEL maintainer="Qwen2.5-VL Team"
LABEL description="Stress testing tool for Qwen2.5-VL multimodal dialogue API"
LABEL version="0.1.0"
