#!/bin/bash

# Qwen2.5-VL Stress Testing Tool Demo Script
# This script demonstrates the capabilities of the stress testing tool

set -e

echo "🚀 Qwen2.5-VL Stress Testing Tool Demo"
echo "======================================"
echo ""

# Check if binary exists
if [ ! -f "target/release/qwen-stress" ]; then
    echo "📦 Building the stress testing tool..."
    cargo build --release
    echo "✅ Build completed!"
    echo ""
fi

echo "📋 Tool Information:"
echo "-------------------"
./target/release/qwen-stress --help
echo ""

echo "🎯 Demo 1: Quick Text-only Stress Test"
echo "--------------------------------------"
echo "Testing with concurrency levels: 1, 2, 4, 8"
echo "Duration per level: 3 seconds"
echo "Results will be saved to benchmark/ directory"
echo ""
./target/release/qwen-stress \
    --mode text \
    --max-concurrency-power 3 \
    --duration 3 \
    --output json \
    --verbose

echo ""
echo "🎯 Demo 2: Image Mode Test"
echo "-------------------------"
echo "Testing image processing capabilities"
echo ""
./target/release/qwen-stress \
    --mode image \
    --max-concurrency-power 2 \
    --duration 2 \
    --output json

echo ""
echo "🎯 Demo 3: Mixed Multimodal Test"
echo "--------------------------------"
echo "Testing mixed multimodal capabilities"
echo ""
./target/release/qwen-stress \
    --mode mixed \
    --max-concurrency-power 2 \
    --duration 2 \
    --output json

echo ""
echo "📊 Demo 4: Performance Analysis"
echo "-------------------------------"
echo "Running TPS and TTFT analysis on test results..."
echo ""

# Check if Python analysis script exists
if [ -f "tps_ttft_analysis.py" ]; then
    echo "Running enhanced TPS analysis..."
    python3 tps_ttft_analysis.py
    echo ""
fi

if [ -f "performance_analysis.py" ]; then
    echo "Running multi-modal performance analysis..."
    python3 performance_analysis.py
    echo ""
fi

echo ""
echo "📁 Demo 5: Benchmark Directory Structure"
echo "----------------------------------------"
echo "Test results are organized in the benchmark/ directory:"
echo ""
if [ -d "benchmark" ]; then
    ls -la benchmark/
else
    echo "No benchmark directory found yet. Run some tests first!"
fi

echo ""
echo "🔧 Usage Examples"
echo "----------------"
echo ""
echo "Basic usage:"
echo "  ./target/release/qwen-stress --mode text --output json"
echo ""
echo "Custom endpoint:"
echo "  ./target/release/qwen-stress --url 'http://your-api-endpoint.com/v1/chat/completions'"
echo ""
echo "High concurrency test:"
echo "  ./target/release/qwen-stress --max-concurrency-power 10 --duration 30"
echo ""
echo "Save to specific file:"
echo "  ./target/release/qwen-stress --output-file benchmark/my_test.json"

echo ""
echo "📈 Analysis Tools"
echo "----------------"
echo ""
echo "TPS & TTFT Analysis:"
echo "  python3 tps_ttft_analysis.py [benchmark/test_file.json]"
echo ""
echo "Multi-modal Comparison:"
echo "  python3 performance_analysis.py"
echo ""
echo "Advanced Analysis:"
echo "  python3 advanced_analysis.py"

echo ""
echo "✨ Demo Completed!"
echo "=================="
echo ""
echo "🎉 You've seen the key features of the Qwen2.5-VL Stress Testing Tool."
echo ""
echo "📚 Next Steps:"
echo "  1. Check the benchmark/ directory for your test results"
echo "  2. Run the analysis scripts to get detailed performance insights"
echo "  3. Customize config.toml for your specific testing needs"
echo "  4. Test against your actual Qwen2.5-VL API endpoint"
echo ""
echo "🔗 Key Features:"
echo "  • Automatic benchmark directory organization"
echo "  • Enhanced TPS analysis with commercial insights"
echo "  • Multi-modal performance comparison"
echo "  • Timestamped result files"
echo "  • Comprehensive performance metrics"
echo ""
echo "🚀 Happy stress testing!"
