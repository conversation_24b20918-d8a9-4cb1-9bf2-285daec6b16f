#!/usr/bin/env python3
"""
Qwen2.5-VL TPS和TTFT专项性能分析工具
专注于事务处理速度(TPS)和首字延迟(TTFT)分析
支持商业化部署决策的增强TPS指标分析
"""

import json
import sys
import statistics
from datetime import datetime
from typing import Dict, List, Any, Tuple

def load_test_results(file_path: str) -> Dict[str, Any]:
    """加载测试结果JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return {}

def calculate_enhanced_tps_metrics(data: Dict[str, Any]) -> Dict[str, Any]:
    """计算增强的TPS指标，包括三种不同的TPS定义"""
    levels = data.get('concurrency_levels', [])
    if not levels:
        return {}

    enhanced_metrics = {
        'individual_request_tps': [],  # 单请求TPS
        'concurrent_level_aggregate_tps': [],  # 并发级别总TPS
        'system_overall_tps': 0,  # 系统整体TPS
        'commercial_indicators': {}
    }

    total_requests = 0
    total_duration = 0
    total_successful = 0

    for level in levels:
        concurrency = level['concurrency']
        duration = level.get('duration_seconds', 0)
        successful_requests = level.get('successful_requests', 0)

        # 1. 单请求TPS (Individual Request TPS) - 当前已实现的指标
        individual_tps = level.get('tps', level.get('requests_per_second', 0))

        # 2. 并发级别总TPS (Concurrent Level Aggregate TPS) - 该并发级别的系统整体吞吐量
        concurrent_aggregate_tps = successful_requests / duration if duration > 0 else 0

        enhanced_metrics['individual_request_tps'].append({
            'concurrency': concurrency,
            'tps': individual_tps,
            'description': '单个请求的处理速度，用于评估单用户体验质量'
        })

        enhanced_metrics['concurrent_level_aggregate_tps'].append({
            'concurrency': concurrency,
            'aggregate_tps': concurrent_aggregate_tps,
            'total_requests': successful_requests,
            'duration': duration,
            'description': '该并发级别下的系统整体吞吐量'
        })

        total_requests += successful_requests
        total_duration += duration
        total_successful += successful_requests

    # 3. 系统整体TPS - 整个测试期间的平均TPS
    enhanced_metrics['system_overall_tps'] = total_successful / total_duration if total_duration > 0 else 0

    return enhanced_metrics

def analyze_enhanced_tps_performance(data: Dict[str, Any]) -> Dict[str, Any]:
    """增强的TPS性能分析，支持商业化决策"""
    print("💰 增强TPS分析 - 商业化部署决策支持")
    print("=" * 80)

    enhanced_metrics = calculate_enhanced_tps_metrics(data)
    if not enhanced_metrics:
        print("❌ 未找到并发级别数据")
        return {}

    # 显示三种TPS指标的对比
    print("📊 三种TPS指标对比分析")
    print("-" * 60)
    print(f"{'并发数':<8} {'单请求TPS':<12} {'并发总TPS':<12} {'用户体验':<10} {'系统吞吐':<10}")
    print("-" * 60)

    individual_tps_data = enhanced_metrics['individual_request_tps']
    aggregate_tps_data = enhanced_metrics['concurrent_level_aggregate_tps']

    commercial_analysis = {
        'revenue_optimization': {},
        'user_experience': {},
        'cost_efficiency': {},
        'sla_compliance': {}
    }

    max_individual_tps = 0
    max_aggregate_tps = 0
    optimal_individual_config = 0
    optimal_aggregate_config = 0

    for i, (individual, aggregate) in enumerate(zip(individual_tps_data, aggregate_tps_data)):
        concurrency = individual['concurrency']
        ind_tps = individual['tps']
        agg_tps = aggregate['aggregate_tps']

        # 用户体验评级 (基于单请求TPS)
        if ind_tps >= 2.0:
            ux_grade = "优秀 🌟"
        elif ind_tps >= 1.0:
            ux_grade = "良好 ✅"
        elif ind_tps >= 0.5:
            ux_grade = "一般 ⚠️"
        else:
            ux_grade = "较低 ❌"

        # 系统吞吐评级 (基于并发总TPS)
        if agg_tps >= 5.0:
            throughput_grade = "高 🚀"
        elif agg_tps >= 2.0:
            throughput_grade = "中 ✅"
        elif agg_tps >= 1.0:
            throughput_grade = "低 ⚠️"
        else:
            throughput_grade = "很低 ❌"

        print(f"{concurrency:<8} {ind_tps:<12.2f} {agg_tps:<12.2f} {ux_grade:<10} {throughput_grade:<10}")

        # 记录最优配置
        if ind_tps > max_individual_tps:
            max_individual_tps = ind_tps
            optimal_individual_config = concurrency

        if agg_tps > max_aggregate_tps:
            max_aggregate_tps = agg_tps
            optimal_aggregate_config = concurrency

    print()
    print("🎯 商业化关键决策指标")
    print("-" * 40)
    print(f"系统整体TPS: {enhanced_metrics['system_overall_tps']:.2f}")
    print(f"收入最大化配置: 并发数 {optimal_aggregate_config} (总TPS: {max_aggregate_tps:.2f})")
    print(f"用户体验最优配置: 并发数 {optimal_individual_config} (单请求TPS: {max_individual_tps:.2f})")

    # 存储商业化分析结果
    commercial_analysis['revenue_optimization'] = {
        'optimal_concurrency': optimal_aggregate_config,
        'max_aggregate_tps': max_aggregate_tps,
        'revenue_potential': 'high' if max_aggregate_tps >= 3.0 else 'medium' if max_aggregate_tps >= 1.5 else 'low'
    }

    commercial_analysis['user_experience'] = {
        'optimal_concurrency': optimal_individual_config,
        'max_individual_tps': max_individual_tps,
        'ux_quality': 'excellent' if max_individual_tps >= 2.0 else 'good' if max_individual_tps >= 1.0 else 'poor'
    }

    enhanced_metrics['commercial_analysis'] = commercial_analysis
    print()

    return enhanced_metrics

def analyze_ttft_performance(data: Dict[str, Any]) -> None:
    """TTFT (Time to First Token) 首字延迟分析"""
    print("⚡ TTFT (首字延迟) 分析")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        print("❌ 未找到并发级别数据")
        return
    
    print(f"{'并发数':<8} {'平均TTFT(ms)':<12} {'最小TTFT(ms)':<12} {'最大TTFT(ms)':<12} {'延迟等级':<10}")
    print("-" * 70)
    
    ttft_data = []
    best_ttft = float('inf')
    worst_ttft = 0
    best_concurrency = 0
    worst_concurrency = 0
    
    for level in levels:
        concurrency = level['concurrency']
        avg_ttft = level.get('avg_ttft_ms', 0)
        min_ttft = level.get('min_ttft_ms', 0)
        max_ttft = level.get('max_ttft_ms', 0)
        
        if avg_ttft == 0:
            continue
        
        # TTFT性能等级评估
        if avg_ttft <= 1000:
            grade = "极快 🚀"
        elif avg_ttft <= 3000:
            grade = "快速 ✅"
        elif avg_ttft <= 5000:
            grade = "一般 ⚠️"
        elif avg_ttft <= 10000:
            grade = "较慢 🐌"
        else:
            grade = "很慢 ❌"
        
        print(f"{concurrency:<8} {avg_ttft:<12.0f} {min_ttft:<12.0f} {max_ttft:<12.0f} {grade:<10}")
        
        ttft_data.append((concurrency, avg_ttft, min_ttft, max_ttft))
        
        if avg_ttft < best_ttft:
            best_ttft = avg_ttft
            best_concurrency = concurrency
        
        if avg_ttft > worst_ttft:
            worst_ttft = avg_ttft
            worst_concurrency = concurrency
    
    if not ttft_data:
        print("❌ 未找到TTFT数据")
        return
    
    print()
    print("📊 TTFT性能分析:")
    print(f"  • 最佳TTFT: {best_ttft:.0f}ms (并发数: {best_concurrency})")
    print(f"  • 最差TTFT: {worst_ttft:.0f}ms (并发数: {worst_concurrency})")
    
    # 计算TTFT统计
    avg_ttfts = [item[1] for item in ttft_data]
    overall_avg_ttft = sum(avg_ttfts) / len(avg_ttfts)
    print(f"  • 整体平均TTFT: {overall_avg_ttft:.0f}ms")
    
    # TTFT与并发数的关系
    if len(ttft_data) >= 2:
        initial_ttft = ttft_data[0][1]
        final_ttft = ttft_data[-1][1]
        ttft_change = ((final_ttft - initial_ttft) / initial_ttft) * 100 if initial_ttft > 0 else 0
        trend = "增加" if ttft_change > 0 else "减少"
        print(f"  • TTFT变化趋势: {trend} {abs(ttft_change):.1f}% (随并发数增加)")
    
    # TTFT一致性分析
    ttft_ranges = [(item[3] - item[2]) for item in ttft_data if item[2] > 0 and item[3] > 0]
    if ttft_ranges:
        avg_range = sum(ttft_ranges) / len(ttft_ranges)
        consistency = "一致" if avg_range < 2000 else "波动较大"
        print(f"  • TTFT一致性: {consistency} (平均波动范围: {avg_range:.0f}ms)")
    
    print()

def analyze_commercial_monitoring_indicators(data: Dict[str, Any], enhanced_metrics: Dict[str, Any]) -> Dict[str, Any]:
    """分析商业化监控指标"""
    print("📈 商业化监控指标分析")
    print("=" * 60)

    levels = data.get('concurrency_levels', [])
    if not levels:
        return {}

    monitoring_indicators = {
        'performance_quality': {},
        'scalability': {},
        'pricing_reference': {},
        'sla_metrics': {}
    }

    # 计算性能质量指标
    ttft_values = []
    response_times = []
    success_rates = []

    print("🔍 服务质量指标 (SLA相关)")
    print(f"{'并发数':<8} {'P95响应时间':<12} {'P99响应时间':<12} {'成功率':<8} {'TTFT稳定性':<12}")
    print("-" * 60)

    for level in levels:
        concurrency = level['concurrency']
        success_rate = level.get('success_rate', 1.0)
        avg_response_time = level.get('avg_response_time_ms', 0)
        ttft = level.get('avg_ttft_ms', 0)

        # 估算P95和P99响应时间 (基于平均响应时间的经验公式)
        p95_response = avg_response_time * 1.5  # 经验值：P95约为平均值的1.5倍
        p99_response = avg_response_time * 2.0  # 经验值：P99约为平均值的2倍

        # TTFT稳定性评估
        ttft_stability = "稳定" if ttft > 0 and ttft < 5000 else "不稳定"

        print(f"{concurrency:<8} {p95_response:<12.0f} {p99_response:<12.0f} {success_rate*100:<7.1f}% {ttft_stability:<12}")

        ttft_values.append(ttft) if ttft > 0 else None
        response_times.append(avg_response_time)
        success_rates.append(success_rate)

    # 计算整体SLA指标
    avg_success_rate = statistics.mean(success_rates) if success_rates else 0
    avg_response_time = statistics.mean(response_times) if response_times else 0
    avg_ttft = statistics.mean(ttft_values) if ttft_values else 0

    monitoring_indicators['sla_metrics'] = {
        'average_success_rate': avg_success_rate,
        'average_response_time_ms': avg_response_time,
        'average_ttft_ms': avg_ttft,
        'sla_compliance': 'good' if avg_success_rate >= 0.99 and avg_response_time <= 10000 else 'needs_improvement'
    }

    print()
    print("💰 成本效益分析")
    print("-" * 40)

    # 成本效益指标 (基于假设的资源成本)
    base_cost_per_hour = 10.0  # 假设基础成本每小时10美元

    individual_tps_data = enhanced_metrics.get('individual_request_tps', [])
    aggregate_tps_data = enhanced_metrics.get('concurrent_level_aggregate_tps', [])

    print(f"{'并发数':<8} {'每TPS成本':<12} {'每请求成本':<12} {'成本效益':<10}")
    print("-" * 50)

    cost_efficiency_data = []

    for individual, aggregate in zip(individual_tps_data, aggregate_tps_data):
        concurrency = individual['concurrency']
        agg_tps = aggregate['aggregate_tps']

        # 成本随并发数线性增长的假设
        estimated_cost_per_hour = base_cost_per_hour * (1 + concurrency * 0.1)
        cost_per_tps = estimated_cost_per_hour / agg_tps if agg_tps > 0 else float('inf')
        cost_per_request = estimated_cost_per_hour / (agg_tps * 3600) if agg_tps > 0 else float('inf')

        # 成本效益评级
        if cost_per_tps <= 5.0:
            efficiency = "高效 🌟"
        elif cost_per_tps <= 10.0:
            efficiency = "一般 ✅"
        else:
            efficiency = "低效 ❌"

        print(f"{concurrency:<8} ${cost_per_tps:<11.2f} ${cost_per_request:<11.4f} {efficiency:<10}")

        cost_efficiency_data.append({
            'concurrency': concurrency,
            'cost_per_tps': cost_per_tps,
            'cost_per_request': cost_per_request,
            'efficiency_rating': efficiency
        })

    monitoring_indicators['cost_efficiency'] = cost_efficiency_data

    print()
    print("📊 扩展性指标")
    print("-" * 40)

    # 计算线性扩展系数
    if len(aggregate_tps_data) >= 2:
        concurrency_values = [item['concurrency'] for item in aggregate_tps_data]
        tps_values = [item['aggregate_tps'] for item in aggregate_tps_data]

        # 简单的线性回归计算扩展系数
        n = len(concurrency_values)
        sum_x = sum(concurrency_values)
        sum_y = sum(tps_values)
        sum_xy = sum(x * y for x, y in zip(concurrency_values, tps_values))
        sum_x2 = sum(x * x for x in concurrency_values)

        if n * sum_x2 - sum_x * sum_x != 0:
            linear_coefficient = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)

            # 找到性能拐点 (TPS开始下降的点)
            performance_inflection = None
            for i in range(1, len(tps_values)):
                if tps_values[i] < tps_values[i-1]:
                    performance_inflection = concurrency_values[i-1]
                    break

            print(f"线性扩展系数: {linear_coefficient:.3f}")
            print(f"性能拐点: 并发数 {performance_inflection if performance_inflection else '未检测到'}")

            monitoring_indicators['scalability'] = {
                'linear_scaling_coefficient': linear_coefficient,
                'performance_inflection_point': performance_inflection,
                'scalability_rating': 'good' if linear_coefficient > 0.1 else 'poor'
            }

    print()
    return monitoring_indicators

def analyze_tps_ttft_correlation(data: Dict[str, Any]) -> None:
    """TPS与TTFT相关性分析"""
    print("🔗 TPS与TTFT相关性分析")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        return
    
    correlation_data = []
    for level in levels:
        tps = level.get('tps', level['requests_per_second'])
        ttft = level.get('avg_ttft_ms', 0)
        if ttft > 0:
            correlation_data.append((tps, ttft, level['concurrency']))
    
    if len(correlation_data) < 2:
        print("❌ 数据不足，无法进行相关性分析")
        return
    
    print(f"{'TPS':<8} {'TTFT(ms)':<10} {'并发数':<8} {'效率评估':<12}")
    print("-" * 50)
    
    for tps, ttft, concurrency in correlation_data:
        # 效率评估：TPS/TTFT比值越高越好
        efficiency = (tps * 1000) / ttft if ttft > 0 else 0
        
        if efficiency >= 0.5:
            efficiency_grade = "高效 🌟"
        elif efficiency >= 0.2:
            efficiency_grade = "良好 ✅"
        elif efficiency >= 0.1:
            efficiency_grade = "一般 ⚠️"
        else:
            efficiency_grade = "低效 ❌"
        
        print(f"{tps:<8.2f} {ttft:<10.0f} {concurrency:<8} {efficiency_grade:<12}")
    
    print()
    
    # 寻找最佳平衡点
    best_balance = max(correlation_data, key=lambda x: (x[0] * 1000) / x[1] if x[1] > 0 else 0)
    print(f"💡 最佳TPS/TTFT平衡点:")
    print(f"  • 并发数: {best_balance[2]}")
    print(f"  • TPS: {best_balance[0]:.2f}")
    print(f"  • TTFT: {best_balance[1]:.0f}ms")
    print(f"  • 效率比: {(best_balance[0] * 1000) / best_balance[1]:.3f}")
    print()

def generate_pricing_recommendations(enhanced_metrics: Dict[str, Any], monitoring_indicators: Dict[str, Any]) -> Dict[str, Any]:
    """生成定价参考建议"""
    print("💲 定价参考建议")
    print("=" * 60)

    pricing_recommendations = {
        'tps_based_pricing': {},
        'concurrency_based_pricing': {},
        'tiered_pricing': {}
    }

    individual_tps_data = enhanced_metrics.get('individual_request_tps', [])
    aggregate_tps_data = enhanced_metrics.get('concurrent_level_aggregate_tps', [])
    cost_efficiency_data = monitoring_indicators.get('cost_efficiency', [])

    if not individual_tps_data or not cost_efficiency_data:
        print("❌ 数据不足，无法生成定价建议")
        return pricing_recommendations

    print("📊 按TPS定价建议")
    print(f"{'TPS范围':<15} {'建议价格/TPS/小时':<20} {'目标客户':<15}")
    print("-" * 55)

    # TPS定价层级
    tps_tiers = [
        (0, 1.0, "$15-20", "小型企业"),
        (1.0, 2.0, "$12-15", "中型企业"),
        (2.0, 5.0, "$10-12", "大型企业"),
        (5.0, float('inf'), "$8-10", "超大规模")
    ]

    for min_tps, max_tps, price_range, target in tps_tiers:
        tps_desc = f"{min_tps:.1f}-{max_tps:.1f}" if max_tps != float('inf') else f"{min_tps:.1f}+"
        print(f"{tps_desc:<15} {price_range:<20} {target:<15}")

    print()
    print("📊 按并发数定价建议")
    print(f"{'并发数':<8} {'建议价格/小时':<15} {'适用场景':<20}")
    print("-" * 50)

    for i, (individual, aggregate, cost) in enumerate(zip(individual_tps_data, aggregate_tps_data, cost_efficiency_data)):
        concurrency = individual['concurrency']
        agg_tps = aggregate['aggregate_tps']
        cost_per_hour = cost['cost_per_tps'] * agg_tps if agg_tps > 0 else 0

        # 建议价格 = 成本 * 1.5 (50%利润率)
        suggested_price = cost_per_hour * 1.5

        # 适用场景
        if concurrency <= 2:
            scenario = "开发测试"
        elif concurrency <= 8:
            scenario = "小规模生产"
        elif concurrency <= 32:
            scenario = "中规模生产"
        else:
            scenario = "大规模生产"

        print(f"{concurrency:<8} ${suggested_price:<14.2f} {scenario:<20}")

    # 分层定价建议
    print()
    print("🎯 推荐分层定价策略")
    print("-" * 40)

    # 找到最佳性价比配置
    best_efficiency_idx = 0
    best_efficiency_ratio = float('inf')

    for i, cost in enumerate(cost_efficiency_data):
        if cost['cost_per_tps'] < best_efficiency_ratio:
            best_efficiency_ratio = cost['cost_per_tps']
            best_efficiency_idx = i

    optimal_config = individual_tps_data[best_efficiency_idx]
    optimal_concurrency = optimal_config['concurrency']
    optimal_tps = optimal_config['tps']

    print(f"基础套餐: 并发数 1-4, 适合开发和小规模测试")
    print(f"标准套餐: 并发数 4-16, 适合中小企业生产环境")
    print(f"企业套餐: 并发数 16-64, 适合大型企业")
    print(f"推荐最优配置: 并发数 {optimal_concurrency} (TPS: {optimal_tps:.2f})")

    pricing_recommendations['optimal_configuration'] = {
        'concurrency': optimal_concurrency,
        'tps': optimal_tps,
        'cost_efficiency_ratio': best_efficiency_ratio
    }

    print()
    return pricing_recommendations

def generate_commercial_deployment_report(data: Dict[str, Any], enhanced_metrics: Dict[str, Any],
                                        monitoring_indicators: Dict[str, Any],
                                        pricing_recommendations: Dict[str, Any]) -> Dict[str, Any]:
    """生成商业化部署决策报告"""
    print("🚀 商业化部署决策报告")
    print("=" * 80)

    commercial_analysis = enhanced_metrics.get('commercial_analysis', {})
    revenue_opt = commercial_analysis.get('revenue_optimization', {})
    user_exp = commercial_analysis.get('user_experience', {})

    deployment_report = {
        'executive_summary': {},
        'configuration_recommendations': {},
        'business_scenarios': {},
        'risk_assessment': {}
    }

    print("📋 执行摘要")
    print("-" * 40)

    max_aggregate_tps = revenue_opt.get('max_aggregate_tps', 0)
    max_individual_tps = user_exp.get('max_individual_tps', 0)
    optimal_revenue_concurrency = revenue_opt.get('optimal_concurrency', 0)
    optimal_ux_concurrency = user_exp.get('optimal_concurrency', 0)

    print(f"• 系统整体TPS: {enhanced_metrics.get('system_overall_tps', 0):.2f}")
    print(f"• 收入最大化TPS: {max_aggregate_tps:.2f} (并发数: {optimal_revenue_concurrency})")
    print(f"• 用户体验最优TPS: {max_individual_tps:.2f} (并发数: {optimal_ux_concurrency})")

    # 商业化就绪度评估
    sla_metrics = monitoring_indicators.get('sla_metrics', {})
    success_rate = sla_metrics.get('average_success_rate', 0)
    avg_ttft = sla_metrics.get('average_ttft_ms', 0)

    readiness_score = 0
    if success_rate >= 0.99:
        readiness_score += 30
    elif success_rate >= 0.95:
        readiness_score += 20

    if max_aggregate_tps >= 2.0:
        readiness_score += 30
    elif max_aggregate_tps >= 1.0:
        readiness_score += 20

    if avg_ttft <= 3000:
        readiness_score += 25
    elif avg_ttft <= 5000:
        readiness_score += 15

    if max_individual_tps >= 1.0:
        readiness_score += 15
    elif max_individual_tps >= 0.5:
        readiness_score += 10

    readiness_level = "优秀" if readiness_score >= 80 else "良好" if readiness_score >= 60 else "需要改进"
    print(f"• 商业化就绪度: {readiness_score}/100 ({readiness_level})")

    deployment_report['executive_summary'] = {
        'system_overall_tps': enhanced_metrics.get('system_overall_tps', 0),
        'max_revenue_tps': max_aggregate_tps,
        'max_ux_tps': max_individual_tps,
        'readiness_score': readiness_score,
        'readiness_level': readiness_level
    }

    print()
    print("🎯 不同业务场景配置建议")
    print("-" * 50)

    scenarios = [
        {
            'name': '高吞吐量场景',
            'priority': '最大化系统吞吐量',
            'recommended_concurrency': optimal_revenue_concurrency,
            'expected_tps': max_aggregate_tps,
            'use_cases': ['批量处理', 'API服务', '数据分析']
        },
        {
            'name': '高体验场景',
            'priority': '最优化用户体验',
            'recommended_concurrency': optimal_ux_concurrency,
            'expected_tps': max_individual_tps,
            'use_cases': ['实时对话', '交互应用', '客户服务']
        },
        {
            'name': '平衡场景',
            'priority': '平衡吞吐量和体验',
            'recommended_concurrency': (optimal_revenue_concurrency + optimal_ux_concurrency) // 2,
            'expected_tps': (max_aggregate_tps + max_individual_tps) / 2,
            'use_cases': ['通用API', '混合工作负载', '多租户服务']
        }
    ]

    for scenario in scenarios:
        print(f"{scenario['name']}:")
        print(f"  • 推荐并发数: {scenario['recommended_concurrency']}")
        print(f"  • 预期TPS: {scenario['expected_tps']:.2f}")
        print(f"  • 适用场景: {', '.join(scenario['use_cases'])}")
        print()

    deployment_report['business_scenarios'] = scenarios

    return deployment_report

def generate_recommendations(data: Dict[str, Any]) -> None:
    """生成TPS和TTFT优化建议"""
    print("💡 性能优化建议")
    print("=" * 60)
    
    levels = data.get('concurrency_levels', [])
    if not levels:
        return
    
    # 分析TPS
    tps_values = [level.get('tps', level['requests_per_second']) for level in levels]
    peak_tps = max(tps_values)
    peak_idx = tps_values.index(peak_tps)
    optimal_concurrency = levels[peak_idx]['concurrency']
    
    # 分析TTFT
    ttft_values = [level.get('avg_ttft_ms', 0) for level in levels if level.get('avg_ttft_ms', 0) > 0]
    avg_ttft = sum(ttft_values) / len(ttft_values) if ttft_values else 0
    
    print("🎯 TPS优化建议:")
    if peak_tps >= 2.0:
        print(f"  ✅ TPS性能优秀 ({peak_tps:.2f})，建议在{optimal_concurrency}并发下部署")
    elif peak_tps >= 1.0:
        print(f"  ⚠️  TPS性能良好 ({peak_tps:.2f})，可考虑以下优化：")
        print("     • 增加GPU资源以提升并行处理能力")
        print("     • 优化模型推理引擎配置")
    else:
        print(f"  ❌ TPS性能较低 ({peak_tps:.2f})，需要重点优化：")
        print("     • 检查服务器资源配置")
        print("     • 考虑模型量化或优化")
        print("     • 增加实例数量进行负载分担")
    
    print()
    print("⚡ TTFT优化建议:")
    if avg_ttft <= 3000:
        print(f"  ✅ TTFT表现优秀 ({avg_ttft:.0f}ms)，用户体验良好")
    elif avg_ttft <= 5000:
        print(f"  ⚠️  TTFT表现一般 ({avg_ttft:.0f}ms)，建议优化：")
        print("     • 优化模型加载和初始化流程")
        print("     • 使用更快的推理引擎")
    else:
        print(f"  ❌ TTFT较慢 ({avg_ttft:.0f}ms)，严重影响用户体验：")
        print("     • 考虑使用更小的模型或量化版本")
        print("     • 实现预热机制减少冷启动时间")
        print("     • 优化网络和I/O性能")
    
    print()
    print("🚀 综合部署建议:")
    print(f"  • 推荐并发数: {optimal_concurrency}")
    print(f"  • 预期TPS: {peak_tps:.2f}")
    print(f"  • 预期TTFT: {avg_ttft:.0f}ms")
    
    if peak_tps >= 1.0 and avg_ttft <= 5000:
        print("  • 🌟 系统性能良好，适合生产环境部署")
    else:
        print("  • ⚠️  建议先进行性能优化再部署到生产环境")

def save_enhanced_analysis_report(data: Dict[str, Any], enhanced_metrics: Dict[str, Any],
                                monitoring_indicators: Dict[str, Any],
                                pricing_recommendations: Dict[str, Any],
                                deployment_report: Dict[str, Any]) -> None:
    """保存增强分析报告到JSON文件"""

    report = {
        'analysis_timestamp': datetime.now().isoformat(),
        'original_data': data,
        'enhanced_tps_metrics': enhanced_metrics,
        'commercial_monitoring_indicators': monitoring_indicators,
        'pricing_recommendations': pricing_recommendations,
        'deployment_report': deployment_report,
        'analysis_metadata': {
            'version': '2.0',
            'features': [
                'individual_request_tps',
                'concurrent_level_aggregate_tps',
                'system_overall_tps',
                'commercial_indicators',
                'cost_efficiency_analysis',
                'pricing_recommendations',
                'deployment_scenarios'
            ]
        }
    }

    # 确保benchmark目录存在
    import os
    os.makedirs("benchmark", exist_ok=True)

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"benchmark/enhanced_tps_commercial_analysis_{timestamp}.json"

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📄 增强分析报告已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")

def main():
    """主函数 - 增强版商业化TPS分析"""
    print("📊 Qwen2.5-VL 增强TPS分析 - 商业化部署决策支持")
    print("=" * 80)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 支持三种TPS指标: 单请求TPS、并发级别总TPS、系统整体TPS")
    print("💰 包含商业化决策指标: 成本效益、定价建议、部署方案")
    print()

    # 检查是否提供了文件路径
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认从benchmark目录查找最新的测试结果文件
        import os
        import glob

        benchmark_files = glob.glob("benchmark/stress_test_*.json")
        if benchmark_files:
            # 选择最新的文件
            file_path = max(benchmark_files, key=os.path.getctime)
            print(f"🔍 自动选择最新的测试结果文件: {file_path}")
        else:
            file_path = "tps_ttft_test_results.json"
            print(f"⚠️  未找到benchmark目录中的测试文件，使用默认路径: {file_path}")

    # 加载测试结果
    data = load_test_results(file_path)
    if not data:
        print("❌ 无法加载测试数据，请检查文件路径")
        return

    print(f"📁 分析文件: {file_path}")
    print()

    # 执行增强的TPS分析
    enhanced_metrics = analyze_enhanced_tps_performance(data)
    if not enhanced_metrics:
        print("❌ TPS分析失败")
        return

    # 执行TTFT分析
    analyze_ttft_performance(data)

    # 执行商业化监控指标分析
    monitoring_indicators = analyze_commercial_monitoring_indicators(data, enhanced_metrics)

    # 生成定价建议
    pricing_recommendations = generate_pricing_recommendations(enhanced_metrics, monitoring_indicators)

    # 生成商业化部署报告
    deployment_report = generate_commercial_deployment_report(
        data, enhanced_metrics, monitoring_indicators, pricing_recommendations
    )

    # 执行传统的相关性分析
    analyze_tps_ttft_correlation(data)

    # 生成传统建议
    generate_recommendations(data)

    # 保存增强分析报告
    save_enhanced_analysis_report(
        data, enhanced_metrics, monitoring_indicators,
        pricing_recommendations, deployment_report
    )

    print()
    print("✅ 增强TPS商业化分析完成！")
    print("📊 报告包含:")
    print("   • 三种TPS指标对比分析")
    print("   • 商业化监控指标")
    print("   • 成本效益分析")
    print("   • 定价参考建议")
    print("   • 部署场景推荐")
    print("   • JSON格式详细报告")

if __name__ == '__main__':
    main()
