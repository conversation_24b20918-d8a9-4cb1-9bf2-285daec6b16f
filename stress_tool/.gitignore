# Rust build artifacts
/target/
Cargo.lock

# Test results and benchmark data
benchmark/
*.json
!config.toml

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Temporary files
/tmp/
*.tmp
*.temp

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Log files
*.log

# Environment files
.env
.env.local
.env.*.local

# Reports (keep only templates)
*_results.json
*_analysis.json
*_report.txt
!*_TEMPLATE.md
!*_REPORT.md
