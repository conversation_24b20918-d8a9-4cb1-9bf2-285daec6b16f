#!/usr/bin/env python3
"""
Qwen2.5-VL Token Calculator
计算图像、视频对话请求的token消耗量

使用方法:
python token_calculator.py --input "你的对话请求" --image "图片路径" --video "视频路径"
"""

import argparse
import json
import math
import os
import sys
from typing import Dict, List, Optional, Tuple, Union
from PIL import Image
import requests
from io import BytesIO
import base64

# 常量定义 (基于qwen-vl-utils源码)
IMAGE_FACTOR = 28
MIN_PIXELS = 4 * 28 * 28  # 3,136
MAX_PIXELS = 16384 * 28 * 28  # 12,845,056
MAX_RATIO = 200

VIDEO_MIN_PIXELS = 128 * 28 * 28  # 100,352
VIDEO_MAX_PIXELS = 768 * 28 * 28  # 602,112
FRAME_FACTOR = 2
FPS = 2.0
FPS_MIN_FRAMES = 4
FPS_MAX_FRAMES = 768

# 视频总像素限制 (默认为128K tokens的90%)
VIDEO_TOTAL_PIXELS = int(128000 * 28 * 28 * 0.9)  # 90,316,800

# 空间合并大小 (用于token计算)
SPATIAL_MERGE_SIZE = 2


def round_by_factor(number: int, factor: int) -> int:
    """返回最接近number且能被factor整除的整数"""
    return round(number / factor) * factor


def ceil_by_factor(number: int, factor: int) -> int:
    """返回大于等于number且能被factor整除的最小整数"""
    return math.ceil(number / factor) * factor


def floor_by_factor(number: int, factor: int) -> int:
    """返回小于等于number且能被factor整除的最大整数"""
    return math.floor(number / factor) * factor


def smart_resize(
    height: int, 
    width: int, 
    factor: int = IMAGE_FACTOR, 
    min_pixels: int = MIN_PIXELS, 
    max_pixels: int = MAX_PIXELS
) -> Tuple[int, int]:
    """
    智能调整图像尺寸，确保：
    1. 高度和宽度都能被factor整除
    2. 总像素数在[min_pixels, max_pixels]范围内
    3. 尽可能保持宽高比
    """
    if max(height, width) / min(height, width) > MAX_RATIO:
        raise ValueError(
            f"宽高比必须小于{MAX_RATIO}，当前为{max(height, width) / min(height, width)}"
        )
    
    h_bar = max(factor, round_by_factor(height, factor))
    w_bar = max(factor, round_by_factor(width, factor))
    
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = max(factor, floor_by_factor(height / beta, factor))
        w_bar = max(factor, floor_by_factor(width / beta, factor))
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = ceil_by_factor(height * beta, factor)
        w_bar = ceil_by_factor(width * beta, factor)
    
    return h_bar, w_bar


def load_image(image_path: str) -> Image.Image:
    """加载图像，支持本地文件、HTTP URL和base64"""
    if image_path.startswith(('http://', 'https://')):
        response = requests.get(image_path)
        image = Image.open(BytesIO(response.content))
    elif image_path.startswith('data:image'):
        # base64格式
        header, data = image_path.split(',', 1)
        image_data = base64.b64decode(data)
        image = Image.open(BytesIO(image_data))
    else:
        # 本地文件
        image = Image.open(image_path)
    
    return image.convert('RGB')


def calculate_image_tokens(
    image_path: str, 
    min_pixels: int = MIN_PIXELS, 
    max_pixels: int = MAX_PIXELS
) -> Dict:
    """计算单张图像的token数量"""
    try:
        image = load_image(image_path)
        original_width, original_height = image.size
        
        # 智能调整尺寸
        resized_height, resized_width = smart_resize(
            original_height, original_width, 
            factor=IMAGE_FACTOR, 
            min_pixels=min_pixels, 
            max_pixels=max_pixels
        )
        
        # 计算token数量
        # 公式: (resized_height / 28) * (resized_width / 28) / merge_size²
        tokens = (resized_height // IMAGE_FACTOR) * (resized_width // IMAGE_FACTOR) // (SPATIAL_MERGE_SIZE ** 2)
        
        return {
            'type': 'image',
            'original_size': (original_width, original_height),
            'resized_size': (resized_width, resized_height),
            'original_pixels': original_width * original_height,
            'resized_pixels': resized_width * resized_height,
            'tokens': tokens,
            'grid_size': (resized_height // IMAGE_FACTOR, resized_width // IMAGE_FACTOR)
        }
    except Exception as e:
        return {'type': 'image', 'error': str(e), 'tokens': 0}


def get_video_info(video_path: str) -> Dict:
    """获取视频基本信息"""
    try:
        # 尝试使用decord
        try:
            from decord import VideoReader
            vr = VideoReader(video_path)
            return {
                'duration': len(vr) / vr.get_avg_fps(),
                'fps': vr.get_avg_fps(),
                'total_frames': len(vr),
                'width': vr[0].shape[1],
                'height': vr[0].shape[0]
            }
        except ImportError:
            pass

        # 尝试使用opencv
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()

            return {
                'duration': frame_count / fps if fps > 0 else 0,
                'fps': fps,
                'total_frames': frame_count,
                'width': width,
                'height': height
            }
        except ImportError:
            pass

        # 如果都没有，返回默认值
        print(f"警告: 无法获取视频信息，使用默认值。请安装decord或opencv-python")
        return {
            'duration': 60.0,
            'fps': 30.0,
            'total_frames': 1800,
            'width': 1920,
            'height': 1080
        }
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return {
            'duration': 60.0,
            'fps': 30.0,
            'total_frames': 1800,
            'width': 1920,
            'height': 1080
        }


def calculate_video_tokens(
    video_path: str,
    nframes: Optional[int] = None,
    fps: Optional[float] = None,
    min_pixels: int = VIDEO_MIN_PIXELS,
    total_pixels: int = VIDEO_TOTAL_PIXELS
) -> Dict:
    """计算视频的token数量"""
    try:
        # 获取视频信息（实际应该使用视频处理库）
        video_info = get_video_info(video_path)
        
        # 计算采样帧数
        if nframes is None:
            if fps is None:
                fps = FPS
            nframes = int(fps * video_info['duration'])
        
        # 应用帧数限制
        nframes = max(FPS_MIN_FRAMES, min(nframes, FPS_MAX_FRAMES))
        nframes = ceil_by_factor(nframes, FRAME_FACTOR)
        
        # 计算每帧最大像素数
        max_pixels_per_frame = max(
            min(VIDEO_MAX_PIXELS, total_pixels / nframes * FRAME_FACTOR),
            int(min_pixels * 1.05)
        )
        
        # 智能调整帧尺寸
        resized_height, resized_width = smart_resize(
            video_info['height'], video_info['width'],
            factor=IMAGE_FACTOR,
            min_pixels=min_pixels,
            max_pixels=max_pixels_per_frame
        )
        
        # 计算token数量
        # 公式: (num_frames / 2) * (resized_height / 28) * (resized_width / 28)
        tokens = (nframes // FRAME_FACTOR) * (resized_height // IMAGE_FACTOR) * (resized_width // IMAGE_FACTOR)
        
        return {
            'type': 'video',
            'original_size': (video_info['width'], video_info['height']),
            'resized_size': (resized_width, resized_height),
            'duration': video_info['duration'],
            'original_fps': video_info['fps'],
            'sample_fps': fps or FPS,
            'total_frames': video_info['total_frames'],
            'sampled_frames': nframes,
            'max_pixels_per_frame': max_pixels_per_frame,
            'tokens': tokens,
            'grid_size': (nframes, resized_height // IMAGE_FACTOR, resized_width // IMAGE_FACTOR)
        }
    except Exception as e:
        return {'type': 'video', 'error': str(e), 'tokens': 0}


def calculate_text_tokens(text: str, use_tokenizer: bool = True) -> Dict:
    """计算文本token数量"""
    if use_tokenizer:
        try:
            from transformers import AutoTokenizer
            # 尝试加载Qwen2.5-VL的tokenizer
            tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2.5-VL-7B-Instruct")

            # 模拟对话模板
            messages = [{"role": "user", "content": text}]
            formatted_text = tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )

            # 计算token数量
            tokens = tokenizer.encode(formatted_text)
            actual_tokens = len(tokens)

            return {
                'type': 'text',
                'text_length': len(text),
                'formatted_text_length': len(formatted_text),
                'actual_tokens': actual_tokens,
                'tokens': actual_tokens,
                'method': 'tokenizer'
            }
        except Exception as e:
            print(f"使用tokenizer失败，回退到估算方法: {e}")

    # 回退到估算方法
    chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
    other_chars = len(text) - chinese_chars

    # 改进的估算：中文约1.3字符/token，英文约3.5字符/token
    estimated_tokens = int(chinese_chars / 1.3 + other_chars / 3.5)

    # 对话模板的额外token
    template_tokens = 25  # <|im_start|>user\n...content...<|im_end|>\n<|im_start|>assistant\n

    total_tokens = estimated_tokens + template_tokens

    return {
        'type': 'text',
        'text_length': len(text),
        'chinese_chars': chinese_chars,
        'other_chars': other_chars,
        'estimated_tokens': estimated_tokens,
        'template_tokens': template_tokens,
        'tokens': total_tokens,
        'method': 'estimation'
    }


def calculate_conversation_tokens(
    text: str,
    images: Optional[List[str]] = None,
    videos: Optional[List[str]] = None,
    image_config: Optional[Dict] = None,
    video_config: Optional[Dict] = None
) -> Dict:
    """计算整个对话的token消耗"""
    result = {
        'text': calculate_text_tokens(text),
        'images': [],
        'videos': [],
        'total_tokens': 0,
        'breakdown': {}
    }
    
    # 处理图像
    if images:
        image_cfg = image_config or {}
        for img_path in images:
            img_result = calculate_image_tokens(
                img_path,
                min_pixels=image_cfg.get('min_pixels', MIN_PIXELS),
                max_pixels=image_cfg.get('max_pixels', MAX_PIXELS)
            )
            result['images'].append(img_result)
    
    # 处理视频
    if videos:
        video_cfg = video_config or {}
        for vid_path in videos:
            vid_result = calculate_video_tokens(
                vid_path,
                nframes=video_cfg.get('nframes'),
                fps=video_cfg.get('fps'),
                min_pixels=video_cfg.get('min_pixels', VIDEO_MIN_PIXELS),
                total_pixels=video_cfg.get('total_pixels', VIDEO_TOTAL_PIXELS)
            )
            result['videos'].append(vid_result)
    
    # 计算总token数
    text_tokens = result['text']['tokens']
    image_tokens = sum(img.get('tokens', 0) for img in result['images'])
    video_tokens = sum(vid.get('tokens', 0) for vid in result['videos'])
    
    result['total_tokens'] = text_tokens + image_tokens + video_tokens
    result['breakdown'] = {
        'text_tokens': text_tokens,
        'image_tokens': image_tokens,
        'video_tokens': video_tokens,
        'image_count': len(result['images']),
        'video_count': len(result['videos'])
    }
    
    return result


def main():
    parser = argparse.ArgumentParser(description='Qwen2.5-VL Token Calculator')
    parser.add_argument('--text', '-t', required=True, help='对话文本内容')
    parser.add_argument('--images', '-i', nargs='*', help='图像文件路径列表')
    parser.add_argument('--videos', '-v', nargs='*', help='视频文件路径列表')
    parser.add_argument('--output', '-o', help='输出结果到JSON文件')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    
    # 图像配置
    parser.add_argument('--image-min-pixels', type=int, default=MIN_PIXELS, help='图像最小像素数')
    parser.add_argument('--image-max-pixels', type=int, default=MAX_PIXELS, help='图像最大像素数')
    
    # 视频配置
    parser.add_argument('--video-fps', type=float, help='视频采样帧率')
    parser.add_argument('--video-nframes', type=int, help='视频采样帧数')
    parser.add_argument('--video-min-pixels', type=int, default=VIDEO_MIN_PIXELS, help='视频每帧最小像素数')
    parser.add_argument('--video-total-pixels', type=int, default=VIDEO_TOTAL_PIXELS, help='视频总像素限制')
    
    args = parser.parse_args()
    
    # 配置参数
    image_config = {
        'min_pixels': args.image_min_pixels,
        'max_pixels': args.image_max_pixels
    }
    
    video_config = {
        'fps': args.video_fps,
        'nframes': args.video_nframes,
        'min_pixels': args.video_min_pixels,
        'total_pixels': args.video_total_pixels
    }
    
    # 计算token
    result = calculate_conversation_tokens(
        text=args.text,
        images=args.images,
        videos=args.videos,
        image_config=image_config,
        video_config=video_config
    )
    
    # 输出结果
    if args.verbose:
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"总Token数: {result['total_tokens']}")
        print(f"  - 文本: {result['breakdown']['text_tokens']} tokens")
        print(f"  - 图像: {result['breakdown']['image_tokens']} tokens ({result['breakdown']['image_count']}张)")
        print(f"  - 视频: {result['breakdown']['video_tokens']} tokens ({result['breakdown']['video_count']}个)")
    
    # 保存到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到: {args.output}")


if __name__ == '__main__':
    main()
