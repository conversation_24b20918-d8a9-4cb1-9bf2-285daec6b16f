# Qwen2.5-VL Token Calculator

这是一个用于计算Qwen2.5-VL模型图像、视频对话请求token消耗量的工具。

## 功能特性

- ✅ **图像Token计算**: 支持本地文件、HTTP URL、base64格式
- ✅ **视频Token计算**: 支持多种视频格式，自动帧采样
- ✅ **文本Token计算**: 支持精确tokenizer计算和估算模式
- ✅ **混合内容**: 支持图像+视频+文本的组合计算
- ✅ **配置灵活**: 可调整分辨率、采样率等参数
- ✅ **成本估算**: 提供token成本估算功能

## 安装依赖

```bash
# 基础依赖
pip install pillow requests

# 可选依赖（用于更准确的计算）
pip install transformers  # 用于精确的文本token计算
pip install decord        # 用于视频处理
pip install opencv-python # 备用视频处理库
```

## 快速开始

### 1. 命令行使用

```bash
# 纯文本对话
python token_calculator.py --text "请解释一下深度学习的基本概念"

# 单图像对话
python token_calculator.py --text "请描述这张图片" --images image.jpg

# 视频对话
python token_calculator.py --text "总结视频内容" --videos video.mp4

# 混合内容
python token_calculator.py --text "比较图片和视频" --images img1.jpg img2.jpg --videos video.mp4

# 详细输出
python token_calculator.py --text "分析图片" --images image.jpg --verbose

# 保存结果
python token_calculator.py --text "分析" --images image.jpg --output result.json
```

### 2. Python代码使用

```python
from token_calculator import calculate_conversation_tokens

# 基本使用
result = calculate_conversation_tokens(
    text="请分析这张图片",
    images=["path/to/image.jpg"],
    videos=["path/to/video.mp4"]
)

print(f"总Token数: {result['total_tokens']}")
print(f"文本Token: {result['breakdown']['text_tokens']}")
print(f"图像Token: {result['breakdown']['image_tokens']}")
print(f"视频Token: {result['breakdown']['video_tokens']}")
```

### 3. 运行示例

```bash
python token_calculator_examples.py
```

## Token计算原理

### 图像Token计算

```python
# 1. 智能尺寸调整
resized_height, resized_width = smart_resize(
    original_height, original_width,
    factor=28,  # 必须是28的倍数
    min_pixels=4*28*28,      # 最小3,136像素
    max_pixels=16384*28*28   # 最大12,845,056像素
)

# 2. Token计算
image_tokens = (resized_height // 28) * (resized_width // 28) // 4
```

### 视频Token计算

```python
# 1. 帧数计算
nframes = min(max(fps * duration, 4), 768)  # 4-768帧
nframes = ceil_to_multiple_of_2(nframes)    # 调整为2的倍数

# 2. 每帧尺寸调整（类似图像）
max_pixels_per_frame = min(768*28*28, total_pixels/nframes*2)

# 3. Token计算
video_tokens = (nframes // 2) * (height // 28) * (width // 28)
```

### 文本Token计算

- **精确模式**: 使用transformers tokenizer
- **估算模式**: 中文≈1.3字符/token，英文≈3.5字符/token

## 配置参数

### 图像配置

```python
image_config = {
    'min_pixels': 256 * 28 * 28,    # 最小像素数
    'max_pixels': 1280 * 28 * 28,   # 最大像素数
}
```

### 视频配置

```python
video_config = {
    'fps': 2.0,                     # 采样帧率
    'nframes': 64,                  # 固定帧数（与fps二选一）
    'min_pixels': 128 * 28 * 28,    # 每帧最小像素数
    'total_pixels': 128000 * 28 * 28 * 0.9,  # 总像素限制
}
```

## 使用场景示例

### 场景1: 成本优化

```python
# 低成本配置（适合批量处理）
low_cost_config = {
    'image_config': {
        'min_pixels': 256 * 28 * 28,
        'max_pixels': 512 * 28 * 28
    },
    'video_config': {
        'fps': 1.0,
        'total_pixels': 64000 * 28 * 28
    }
}

# 高质量配置（适合精细分析）
high_quality_config = {
    'image_config': {
        'min_pixels': 512 * 28 * 28,
        'max_pixels': 2048 * 28 * 28
    },
    'video_config': {
        'fps': 4.0,
        'total_pixels': 256000 * 28 * 28
    }
}
```

### 场景2: 批量估算

```python
import json

conversations = [
    {"text": "分析图片", "images": ["img1.jpg"]},
    {"text": "总结视频", "videos": ["vid1.mp4"]},
    {"text": "比较内容", "images": ["img1.jpg"], "videos": ["vid1.mp4"]}
]

total_tokens = 0
results = []

for conv in conversations:
    result = calculate_conversation_tokens(**conv)
    results.append(result)
    total_tokens += result['total_tokens']

print(f"总Token消耗: {total_tokens}")
```

## 性能建议

### 图像优化
- **一般用途**: 256-1280 tokens (min_pixels=256×28×28, max_pixels=1280×28×28)
- **高精度**: 1280-4096 tokens
- **批量处理**: 64-256 tokens

### 视频优化
- **短视频(<1分钟)**: fps=2.0, 建议<5K tokens
- **长视频(>5分钟)**: fps=1.0, 建议<24K tokens
- **关键帧分析**: 使用nframes固定帧数

### 成本控制
- 监控token消耗，避免超出预算
- 对于长视频，考虑分段处理
- 使用低分辨率配置进行初步筛选

## 注意事项

1. **视频处理**: 需要安装decord或opencv-python
2. **网络图片**: 确保网络连接稳定
3. **大文件**: 大视频文件可能需要较长处理时间
4. **内存使用**: 高分辨率内容会消耗更多内存
5. **Token限制**: 单次对话建议控制在128K tokens以内

## 故障排除

### 常见问题

1. **"无法获取视频信息"**
   ```bash
   pip install decord opencv-python
   ```

2. **"使用tokenizer失败"**
   ```bash
   pip install transformers
   ```

3. **网络图片加载失败**
   - 检查网络连接
   - 确认图片URL有效
   - 考虑使用本地文件

4. **内存不足**
   - 降低max_pixels设置
   - 减少视频采样帧数
   - 分批处理大量文件

## 更新日志

- v1.0.0: 初始版本，支持基本的图像、视频、文本token计算
- 支持多种输入格式和配置选项
- 提供详细的使用示例和文档

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
