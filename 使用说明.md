# Qwen2.5-VL Token计算器使用说明

## 📋 概述

这个工具可以帮助您计算Qwen2.5-VL模型处理图像、视频对话请求时的token消耗量，帮助您：
- 预估API调用成本
- 优化输入配置
- 控制token使用量

## 🚀 快速开始

### 1. 安装依赖

```bash
# 基础依赖（必需）
pip install pillow requests

# 可选依赖（推荐安装以获得更准确的结果）
pip install transformers  # 精确文本token计算
pip install decord        # 视频处理
pip install opencv-python # 备用视频处理
```

### 2. 基本使用

#### 方式一：命令行使用

```bash
# 纯文本
python3 token_calculator.py --text "请解释深度学习的基本概念"

# 图像分析
python3 token_calculator.py --text "请描述这张图片" --images image.jpg

# 视频分析  
python3 token_calculator.py --text "总结视频内容" --videos video.mp4

# 混合内容
python3 token_calculator.py --text "比较图片和视频" --images img1.jpg --videos video.mp4

# 详细输出
python3 token_calculator.py --text "分析内容" --images image.jpg --verbose

# 保存结果
python3 token_calculator.py --text "分析" --images image.jpg --output result.json
```

#### 方式二：交互式使用

```bash
python3 quick_token_calc.py
```

然后按提示输入：
1. 对话文本
2. 图像文件路径
3. 视频文件路径  
4. 选择质量配置

#### 方式三：Python代码

```python
from token_calculator import calculate_conversation_tokens

result = calculate_conversation_tokens(
    text="请分析这张图片",
    images=["path/to/image.jpg"],
    videos=["path/to/video.mp4"]
)

print(f"总Token数: {result['total_tokens']}")
```

## 📊 Token计算原理

### 图像Token计算

```
1. 智能尺寸调整：
   - 保持宽高比
   - 尺寸调整为28的倍数
   - 控制像素数在合理范围内

2. Token计算公式：
   image_tokens = (调整后高度 ÷ 28) × (调整后宽度 ÷ 28) ÷ 4
```

**示例**：
- 原图：1920×1080
- 调整后：1400×784 (保持比例，28的倍数)
- Token数：(1400÷28) × (784÷28) ÷ 4 = 50 × 28 ÷ 4 = 350 tokens

### 视频Token计算

```
1. 帧数计算：
   - 根据fps和时长计算采样帧数
   - 限制在4-768帧范围内
   - 调整为2的倍数

2. Token计算公式：
   video_tokens = (帧数 ÷ 2) × (高度 ÷ 28) × (宽度 ÷ 28)
```

**示例**：
- 视频：60秒，1920×1080
- 采样：2fps，共120帧
- 调整后帧尺寸：392×392
- Token数：(120÷2) × (392÷28) × (392÷28) = 60 × 14 × 14 = 11,760 tokens

### 文本Token计算

- **精确模式**：使用transformers tokenizer
- **估算模式**：中文≈1.3字符/token，英文≈3.5字符/token

## ⚙️ 配置选项

### 图像配置

```python
image_config = {
    'min_pixels': 256 * 28 * 28,    # 最小像素数 (约200K像素)
    'max_pixels': 1280 * 28 * 28,   # 最大像素数 (约1M像素)
}
```

### 视频配置

```python
video_config = {
    'fps': 2.0,                     # 采样帧率
    'nframes': 64,                  # 固定帧数（与fps二选一）
    'min_pixels': 128 * 28 * 28,    # 每帧最小像素数
    'total_pixels': 128000 * 28 * 28 * 0.9,  # 总像素限制
}
```

## 💡 使用建议

### 成本优化配置

```bash
# 经济模式（低token消耗）
python3 token_calculator.py \
  --text "分析图片" \
  --images image.jpg \
  --image-min-pixels 200704 \
  --image-max-pixels 401408 \
  --video-fps 1.0
```

### 高质量配置

```bash
# 高质量模式（高token消耗）
python3 token_calculator.py \
  --text "详细分析" \
  --images image.jpg \
  --image-max-pixels 1605632 \
  --video-fps 4.0
```

### 批量处理

```bash
# 创建配置文件 batch_config.json
python3 quick_token_calc.py batch
```

## 📈 性能参考

| 内容类型 | 配置 | 大致Token范围 |
|---------|------|--------------|
| 纯文本 | 100字中文 | 80-120 tokens |
| 单张图片 | 1920×1080, 标准配置 | 300-800 tokens |
| 单张图片 | 1920×1080, 高质量 | 800-2000 tokens |
| 短视频 | 30秒, 2fps | 2000-8000 tokens |
| 长视频 | 5分钟, 1fps | 8000-25000 tokens |

## 🔧 故障排除

### 常见问题

1. **ModuleNotFoundError: No module named 'PIL'**
   ```bash
   pip install pillow
   ```

2. **无法获取视频信息**
   ```bash
   pip install decord opencv-python
   ```

3. **网络图片加载失败**
   - 检查网络连接
   - 使用本地文件测试

4. **内存不足**
   - 降低max_pixels设置
   - 减少视频采样帧数

### 测试脚本

```bash
# 测试基本功能
python3 -c "
from token_calculator import calculate_text_tokens
result = calculate_text_tokens('测试文本')
print(f'Token数: {result[\"tokens\"]}')
"
```

## 📝 示例场景

### 场景1：文档分析
```bash
python3 token_calculator.py \
  --text "请分析这份文档的主要内容并总结要点" \
  --images document1.jpg document2.jpg document3.jpg
```

### 场景2：视频内容审核
```bash
python3 token_calculator.py \
  --text "请检查视频内容是否符合规范" \
  --videos content_video.mp4 \
  --video-fps 1.0
```

### 场景3：多模态对比
```bash
python3 token_calculator.py \
  --text "比较图片和视频中的产品特点，生成对比报告" \
  --images product_image.jpg \
  --videos product_demo.mp4
```

## 📞 支持

如果遇到问题或有改进建议，请：
1. 检查依赖是否正确安装
2. 查看错误信息和日志
3. 尝试使用简化配置
4. 参考示例代码

---

**注意**：此工具基于Qwen2.5-VL的公开文档和代码实现，实际token消耗可能因模型版本和配置而略有差异。建议在正式使用前进行小规模测试验证。
