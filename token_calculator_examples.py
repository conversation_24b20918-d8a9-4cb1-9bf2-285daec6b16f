#!/usr/bin/env python3
"""
Qwen2.5-VL Token Calculator 使用示例
"""

from token_calculator import calculate_conversation_tokens, calculate_image_tokens, calculate_video_tokens, calculate_text_tokens
import json

def example_text_only():
    """纯文本对话示例"""
    print("=" * 50)
    print("示例1: 纯文本对话")
    print("=" * 50)
    
    text = "请帮我分析一下这个问题：如何优化深度学习模型的训练速度？"
    result = calculate_text_tokens(text)
    
    print(f"文本: {text}")
    print(f"Token数量: {result['tokens']}")
    print(f"计算方法: {result['method']}")
    print()

def example_single_image():
    """单图像对话示例"""
    print("=" * 50)
    print("示例2: 单图像对话")
    print("=" * 50)
    
    text = "请描述这张图片的内容"
    # 使用网络图片示例
    image_url = "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg"
    
    result = calculate_conversation_tokens(
        text=text,
        images=[image_url]
    )
    
    print(f"文本: {text}")
    print(f"图像: {image_url}")
    print(f"总Token数: {result['total_tokens']}")
    print(f"  - 文本Token: {result['breakdown']['text_tokens']}")
    print(f"  - 图像Token: {result['breakdown']['image_tokens']}")
    
    if result['images']:
        img_info = result['images'][0]
        if 'error' not in img_info:
            print(f"  - 图像原始尺寸: {img_info['original_size']}")
            print(f"  - 图像调整后尺寸: {img_info['resized_size']}")
            print(f"  - 图像网格大小: {img_info['grid_size']}")
    print()

def example_multiple_images():
    """多图像对话示例"""
    print("=" * 50)
    print("示例3: 多图像对话")
    print("=" * 50)
    
    text = "比较这两张图片的异同点"
    images = [
        "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg",
        "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg"  # 示例用同一张图
    ]
    
    result = calculate_conversation_tokens(
        text=text,
        images=images
    )
    
    print(f"文本: {text}")
    print(f"图像数量: {len(images)}")
    print(f"总Token数: {result['total_tokens']}")
    print(f"  - 文本Token: {result['breakdown']['text_tokens']}")
    print(f"  - 图像Token: {result['breakdown']['image_tokens']}")
    print(f"  - 平均每张图像Token: {result['breakdown']['image_tokens'] // len(images) if images else 0}")
    print()

def example_video():
    """视频对话示例"""
    print("=" * 50)
    print("示例4: 视频对话")
    print("=" * 50)
    
    text = "请总结这个视频的主要内容"
    # 注意：这里使用模拟的视频路径，实际使用时需要提供真实的视频文件
    video_path = "sample_video.mp4"
    
    result = calculate_conversation_tokens(
        text=text,
        videos=[video_path]
    )
    
    print(f"文本: {text}")
    print(f"视频: {video_path}")
    print(f"总Token数: {result['total_tokens']}")
    print(f"  - 文本Token: {result['breakdown']['text_tokens']}")
    print(f"  - 视频Token: {result['breakdown']['video_tokens']}")
    
    if result['videos']:
        vid_info = result['videos'][0]
        if 'error' not in vid_info:
            print(f"  - 视频时长: {vid_info['duration']:.1f}秒")
            print(f"  - 采样帧数: {vid_info['sampled_frames']}")
            print(f"  - 帧尺寸: {vid_info['resized_size']}")
            print(f"  - 网格大小: {vid_info['grid_size']}")
    print()

def example_mixed_content():
    """混合内容对话示例"""
    print("=" * 50)
    print("示例5: 混合内容对话")
    print("=" * 50)
    
    text = "请分析这张图片和视频中的相关性，并给出详细的比较报告"
    image_url = "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg"
    video_path = "sample_video.mp4"
    
    result = calculate_conversation_tokens(
        text=text,
        images=[image_url],
        videos=[video_path]
    )
    
    print(f"文本: {text}")
    print(f"包含: 1张图片 + 1个视频")
    print(f"总Token数: {result['total_tokens']}")
    print(f"  - 文本Token: {result['breakdown']['text_tokens']}")
    print(f"  - 图像Token: {result['breakdown']['image_tokens']}")
    print(f"  - 视频Token: {result['breakdown']['video_tokens']}")
    print()

def example_high_resolution():
    """高分辨率图像示例"""
    print("=" * 50)
    print("示例6: 不同分辨率配置对比")
    print("=" * 50)
    
    text = "请详细分析这张高分辨率图片"
    image_url = "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg"
    
    # 默认配置
    result_default = calculate_conversation_tokens(
        text=text,
        images=[image_url]
    )
    
    # 低分辨率配置
    result_low = calculate_conversation_tokens(
        text=text,
        images=[image_url],
        image_config={
            'min_pixels': 256 * 28 * 28,
            'max_pixels': 512 * 28 * 28
        }
    )
    
    # 高分辨率配置
    result_high = calculate_conversation_tokens(
        text=text,
        images=[image_url],
        image_config={
            'min_pixels': 256 * 28 * 28,
            'max_pixels': 2048 * 28 * 28
        }
    )
    
    print(f"文本: {text}")
    print(f"默认配置Token数: {result_default['total_tokens']} (图像: {result_default['breakdown']['image_tokens']})")
    print(f"低分辨率配置Token数: {result_low['total_tokens']} (图像: {result_low['breakdown']['image_tokens']})")
    print(f"高分辨率配置Token数: {result_high['total_tokens']} (图像: {result_high['breakdown']['image_tokens']})")
    print()

def example_video_sampling():
    """视频采样配置示例"""
    print("=" * 50)
    print("示例7: 不同视频采样配置对比")
    print("=" * 50)
    
    text = "请分析这个视频的内容"
    video_path = "sample_video.mp4"
    
    # 默认配置
    result_default = calculate_conversation_tokens(
        text=text,
        videos=[video_path]
    )
    
    # 低帧率配置
    result_low_fps = calculate_conversation_tokens(
        text=text,
        videos=[video_path],
        video_config={'fps': 1.0}
    )
    
    # 固定帧数配置
    result_fixed_frames = calculate_conversation_tokens(
        text=text,
        videos=[video_path],
        video_config={'nframes': 64}
    )
    
    print(f"文本: {text}")
    print(f"默认配置Token数: {result_default['total_tokens']} (视频: {result_default['breakdown']['video_tokens']})")
    print(f"低帧率配置Token数: {result_low_fps['total_tokens']} (视频: {result_low_fps['breakdown']['video_tokens']})")
    print(f"固定帧数配置Token数: {result_fixed_frames['total_tokens']} (视频: {result_fixed_frames['breakdown']['video_tokens']})")
    print()

def example_cost_estimation():
    """成本估算示例"""
    print("=" * 50)
    print("示例8: Token成本估算")
    print("=" * 50)
    
    # 假设的定价（仅供参考）
    PRICE_PER_1K_TOKENS = 0.002  # 美元
    
    scenarios = [
        ("纯文本对话", "解释一下量子计算的基本原理", [], []),
        ("单图分析", "分析这张图片", ["https://example.com/image.jpg"], []),
        ("视频分析", "总结视频内容", [], ["sample_video.mp4"]),
        ("复杂多模态", "比较图片和视频的关联性，写一份详细报告", ["image1.jpg", "image2.jpg"], ["video.mp4"])
    ]
    
    print(f"{'场景':<15} {'Token数':<10} {'估算成本(USD)':<15}")
    print("-" * 45)
    
    for name, text, images, videos in scenarios:
        result = calculate_conversation_tokens(text=text, images=images, videos=videos)
        tokens = result['total_tokens']
        cost = tokens / 1000 * PRICE_PER_1K_TOKENS
        print(f"{name:<15} {tokens:<10} ${cost:<14.4f}")
    
    print()

def main():
    """运行所有示例"""
    print("Qwen2.5-VL Token Calculator 使用示例")
    print("=" * 60)
    
    try:
        example_text_only()
        example_single_image()
        example_multiple_images()
        example_video()
        example_mixed_content()
        example_high_resolution()
        example_video_sampling()
        example_cost_estimation()
        
        print("所有示例运行完成！")
        print("\n使用提示:")
        print("1. 对于图像，可以通过调整min_pixels和max_pixels来控制token消耗")
        print("2. 对于视频，可以通过调整fps或nframes来控制token消耗")
        print("3. 建议在实际使用前先用此工具估算token消耗量")
        print("4. 长视频建议控制在24K tokens以下以获得更好效果")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        print("请确保已安装必要的依赖包：pip install pillow requests")

if __name__ == '__main__':
    main()
